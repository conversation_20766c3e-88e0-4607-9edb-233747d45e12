package com.eversec.ebp.serverless.gateway.manager.dto;

import com.eversec.ebp.serverless.gateway.dao.entity.ApiAccessRestrict;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * <p>
 * 封堵策略数据传输对象
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-21
 */
@Getter
@Setter
public class ApiAccessRestrictDto implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("策略ID")
    private Integer id;

    @ApiModelProperty("限制类型：1-IP")
    private Integer restrictionType;

    @ApiModelProperty("动作：允许/拒绝")
    private String action;

    @ApiModelProperty("限制内容：一个IP或一个IP段")
    private String content;

    @ApiModelProperty("资产ID")
    private String assetId;

    @ApiModelProperty("资产名称")
    private String assetName;

    public ApiAccessRestrictDto() {
    }

    /**
     * 从ApiAccessRestrict实体构造DTO
     *
     * @param apiAccessRestrict 封堵策略实体
     */
    public ApiAccessRestrictDto(ApiAccessRestrict apiAccessRestrict) {
        this.id = apiAccessRestrict.getId();
        this.restrictionType = apiAccessRestrict.getRestrictionType();
        this.action = apiAccessRestrict.getAction();
        this.content = apiAccessRestrict.getContent();
        this.assetId = apiAccessRestrict.getAssetId();
        this.assetName = apiAccessRestrict.getAssetName();
    }
}
