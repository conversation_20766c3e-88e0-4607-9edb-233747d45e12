package com.eversec.ebp.serverless.gateway.manager.api.log.scheduler;

import com.eversec.ebp.serverless.gateway.manager.api.log.service.AccessLogInfoService;
import com.eversec.ebp.serverless.gateway.manager.api.log.service.JudgeService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/**
 * 类名称: AttackRiskTimer
 * 类描述:
 * 修改时间: 2025 2025/4/22 16:29
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class AttackRiskTimer {

	@Autowired
	AccessLogInfoService accessLogInfoService;

	@Autowired
	JudgeService judgeService;

	@Value("${gateway.log.judge.enable:true}")
	private boolean judgeEnable;

	@Scheduled(fixedRate = 30000, initialDelay = 30000)
	public void doScheduling() {
		log.debug("研判定时器启动");
		try {
			if (judgeEnable) {
				String batchNo = accessLogInfoService.getOneToJudge();
				if (batchNo == null) {
					log.info("没有需要大模型风险研判的日志，定时器结束");
					return;
				}
				judgeService.judgeByLLM(batchNo);
			}
		} catch (Exception e) {
			log.error("定时研判出现错误", e);
		} finally {
			log.debug("研判定时器结束");
		}
	}
}
