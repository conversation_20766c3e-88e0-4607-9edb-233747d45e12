package com.eversec.ebp.serverless.gateway.manager.service.impl;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.eversec.ebp.serverless.gateway.dao.entity.ApiAccess;
import com.eversec.ebp.serverless.gateway.dao.mapper.ApiAccessMapper;
import com.eversec.ebp.serverless.gateway.manager.dto.ApiAccessQueryDto;
import com.eversec.ebp.serverless.gateway.manager.service.ApiAccessService;
import com.eversec.ebp.serverless.gateway.manager.service.ApiInfoService;
import com.eversec.framework.core.data.QueryResult;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <p>
 * 访问控制表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-22
 */
@Service
public class ApiAccessServiceImpl extends ServiceImpl<ApiAccessMapper, ApiAccess> implements ApiAccessService {

    @Autowired
    private ApiAccessMapper apiAccessMapper;

    @Autowired
    private ApiInfoService apiInfoService;

    /**
     * 分页查询访问策略
     *
     * @param apiAccessQueryDto 查询条件
     * @return 分页结果
     */
    @Override
    public QueryResult<ApiAccess> getPage(ApiAccessQueryDto apiAccessQueryDto) {
        // 设置分页信息 传递参数 页数  每页条数
        Page<ApiAccess> page = new Page<>(apiAccessQueryDto.getPageNum(), apiAccessQueryDto.getPageSize());
        // 查询条件
        Wrapper<ApiAccess> queryWrapper = getApiAccessQueryWrapper(apiAccessQueryDto);
        IPage<ApiAccess> accessPageList = apiAccessMapper.selectPage(page, queryWrapper);
        // 获取查询数据
        List<ApiAccess> apiAccessList = accessPageList.getRecords();

        // 为每个访问策略设置防护资产数量
        for (ApiAccess apiAccess : apiAccessList) {
            if (apiAccess.getId() != null) {
                // 通过 apiInfoService 获取防护资产数量并赋值到 accessAssetCount 字段上
                Integer assetCount = apiInfoService.countByAccessPolicyId(apiAccess.getId());
                apiAccess.setAccessAssetCount(assetCount);
            } else {
                apiAccess.setAccessAssetCount(0);
            }
        }

        QueryResult<ApiAccess> result = new QueryResult<>();
        result.setResultData(apiAccessList);
        result.setTotalRecord(accessPageList.getTotal());
        return result;
    }

    /**
     * 构建查询条件
     *
     * @param condition 查询条件
     * @return 查询包装器
     */
    private Wrapper<ApiAccess> getApiAccessQueryWrapper(ApiAccessQueryDto condition) {
        QueryWrapper<ApiAccess> queryWrapper = new QueryWrapper<>();

        if (StringUtils.isNotBlank(condition.getName())) {
            queryWrapper.like("name", condition.getName());
        }

        if (condition.getIsCrossDomain() != null) {
            queryWrapper.eq("is_cross_domain", condition.getIsCrossDomain());
        }

        if (condition.getIsActive() != null) {
            queryWrapper.eq("is_active", condition.getIsActive());
        }

        queryWrapper.orderByDesc("create_time");
        return queryWrapper;
    }

    /**
     * 新增访问策略
     *
     * @param apiAccess 访问策略信息
     * @return 影响行数
     */
    @Override
    @Transactional(readOnly = false, propagation = Propagation.REQUIRED)
    public Integer add(ApiAccess apiAccess) {
        long currentTime = System.currentTimeMillis();
        apiAccess.setCreateTime(currentTime);
        apiAccess.setUpdateTime(currentTime);
        return apiAccessMapper.insert(apiAccess);
    }
}
