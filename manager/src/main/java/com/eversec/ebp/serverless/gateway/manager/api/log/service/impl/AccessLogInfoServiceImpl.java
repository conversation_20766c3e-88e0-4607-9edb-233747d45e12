package com.eversec.ebp.serverless.gateway.manager.api.log.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.eversec.ebp.serverless.gateway.manager.api.log.dto.AccessLogQueryDto;
import com.eversec.ebp.serverless.gateway.manager.api.log.service.AccessLogInfoService;
import com.eversec.ebp.serverless.gateway.manager.log.domain.AccessLog;
import com.eversec.ebp.serverless.gateway.manager.log.mapper.AccessLogMapper;
import com.eversec.framework.core.data.QueryResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringEscapeUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

@Service
@Slf4j
public class AccessLogInfoServiceImpl extends ServiceImpl<AccessLogMapper, AccessLog> implements AccessLogInfoService {

	static Logger logger = LoggerFactory.getLogger(AccessLogInfoService.class);

	@Override
	public QueryResult<AccessLog> getApiAccessLogPageList(AccessLogQueryDto query) {
		QueryResult<AccessLog> result = new QueryResult<>();
		Long total = this.baseMapper.countAccessLogs(query);
		result.setTotalRecord(total);
		if (total > 0) {
			List<AccessLog> logDtoPageList = this.baseMapper.queryAccessLogs(query, query.getStartPosition(), query.getPageSize());
			result.setResultData(logDtoPageList);
		}
		return result;
	}

	@Override
	public AccessLog getLogAggregation(String batchNo) {
		List<AccessLog> entities = this.baseMapper.queryByBatchNo(batchNo);
		if (entities == null || entities.isEmpty()) {
			log.error("没有查询到日志对象：{}", batchNo);
			return null;
		}
		try {
			StringBuilder buffer = new StringBuilder();
			AccessLog log = null;
			int i = 0;
			if (entities.size() == 1) {
				final String responseData = entities.get(0).getResponseData();
				final JSONObject jsonObject = JSON.parseObject(responseData);
				final String answer = jsonObject.getString("answer");
				if (answer == null) {
					entities.get(0).setAllRspData(responseData);
				} else {
					entities.get(0).setAllRspData(answer);
				}
				return entities.get(0);
			}

			String responseMode = "";
			try {
				final AccessLog accessLog = entities.get(0);
				final JSONObject jsonObject = JSON.parseObject(accessLog.getRequestBody());
				responseMode = jsonObject.getString("response_mode");
			} catch (Exception e) {
				e.printStackTrace();
			}

			if ("blocking".equals(responseMode)) {
				log = entities.get(0);
				for (AccessLog entity : entities) {
					buffer.append(entity.getResponseData());
				}
				final String rsp = buffer.toString();
				JSONObject root;
				// 解析JSON
				try {
					root = JSON.parseObject(rsp);
				} catch (Exception e) {
					e.printStackTrace();
					log.setAllRspData(rsp);
					logger.info("响应日志转换JSON对象异常：{}", rsp);
					return log;
				}
				String data = root.getString("answer");
				log.setAllRspData(data);
				return log;
			}

			Collections.reverse(entities);
			while (true) {
				if (i == entities.size()) {
					break;
				}
				final AccessLog accessLog = entities.get(i);
				final String responseData = accessLog.getResponseData();
				if (responseData.contains("data: {\"event\": \"workflow_finished\", ")) {
					break;
				} else {
					i++;
				}
			}
			for (int j = i; j > 0; j--) {
				log = entities.get(j);
				if (log.getResponseData().contains("\"event\": \"message_end\"")) {
					break;
				} else {
					buffer.append(log.getResponseData());
				}
			}
			String rsp = buffer.toString();
			rsp = rsp.substring(6);
			JSONObject root;
			// 解析JSON
			try {
				root = JSON.parseObject(rsp);
			} catch (Exception e) {
				e.printStackTrace();
				log.setAllRspData(rsp);
				logger.info("响应日志转换JSON对象异常：{}", rsp);
				return log;
			}
			JSONObject data = root.getJSONObject("data");
			String status = data.getString("status");
			if ("stopped".equals(status)) {
				log.setAllRspData("用户中断问答");
			} else {
				JSONObject outputs = data.getJSONObject("outputs");
				String answer = outputs.getString("answer");
				// 输出结果
				String value = StringEscapeUtils.unescapeJava(answer);
				log.setAllRspData(value);
			}
			return log;
		} catch (Exception e) {
			final AccessLog log1 = entities.get(0);

			try {
				final JSONObject jsonObject = JSON.parseObject(log1.getRequestBody());
				log1.setAllRspData(jsonObject.getString(""));
				return log1;
			} catch (Exception e1) {
				e1.printStackTrace();
			}
		}
		return entities.get(0);
	}

	@Override

	public String getOneToJudge() {
		return this.baseMapper.getOneToJudge();
	}

	@Override

	public void updateJudgeStatus(String batchNo) {
		this.baseMapper.updateJudgementStatus(batchNo);
	}

	@Override

	public List<String> getIdListByBatchNo(String batchNo) {
		return this.baseMapper.getLogIdList(batchNo);
	}

	@Override
	public AccessLog getOneByAccessLogId(String id) {
		return this.baseMapper.getOneByAccessLogId(id);
	}

}
