package com.eversec.ebp.serverless.gateway.manager.log.domain;

public class LogConst {

    //阻断日志
    public static final String LOG_INTERCEPT_BIG_REJECT = "LOG_INTERCEPT_BIG_REJECT"; //访问控制拒绝日志
    public static final String LOG_INTERCEPT_SMALL_REJECT_UNTIME = "LOG_INTERCEPT_SMALL_REJECT_UNTIME";//非规定时间访问
    public static final String LOG_INTERCEPT_SMALL_REJECT_UNUA = "LOG_INTERCEPT_SMALL_REJECT_UNUA";//非规定UA访问
    public static final String LOG_INTERCEPT_SMALL_REJECT_UNBLACKIP = "LOG_INTERCEPT_SMALL_REJECT_UNBLACKIP";//黑名单IP访问
    public static final String LOG_INTERCEPT_SMALL_REJECT_UNBLACKUSER = "LOG_INTERCEPT_SMALL_REJECT_UNBLACKUSER";//黑名单账号访问
    public static final String LOG_INTERCEPT_SMALL_REJECT_UNWHITEIP = "LOG_INTERCEPT_SMALL_REJECT_UNWHITEIP";//未在白名单IP访问
    public static final String LOG_INTERCEPT_SMALL_REJECT_UNWHITEUSER = "LOG_INTERCEPT_SMALL_REJECT_UNWHITEUSER";//未在白名单的账号访问
    public static final String LOG_INTERCEPT_SMALL_REJECT_PARAMMISS = "LOG_INTERCEPT_SMALL_REJECT_PARAMMISS";//参数缺失
    public static final String LOG_INTERCEPT_SMALL_REJECT_ERRPARAMS = "LOG_INTERCEPT_SMALL_REJECT_ERRPARAMS";//参数错误


    public static final String LOG_INTERCEPT_BIG_FLOW = "LOG_INTERCEPT_BIG_FLOW";//流控拒绝日志
    public static final String LOG_INTERCEPT_SMALL_FLOW_APIQPS = "LOG_INTERCEPT_SMALL_FLOW_APIQPS";//API总控QPS超限
    public static final String LOG_INTERCEPT_SMALL_FLOW_USERQPS = "LOG_INTERCEPT_SMALL_FLOW_USERQPS";//基于用户的QPS超限
    public static final String LOG_INTERCEPT_SMALL_FLOW_IPQPS = "LOG_INTERCEPT_SMALL_FLOW_IPQPS";//基于IP的QPS超限
    public static final String LOG_INTERCEPT_SMALL_FLOW_APPQPS = "LOG_INTERCEPT_SMALL_FLOW_APPQPS";//基于应用的QPS超限

    public static final String LOG_INTERCEPT_BIG_AUTH = "LOG_INTERCEPT_BIG_AUTH";//认证拒绝日志
    public static final String LOG_INTERCEPT_SMALL_AUTH_PWD = "LOG_INTERCEPT_SMALL_AUTH_PWD";//账号口令错误
    public static final String LOG_INTERCEPT_SMALL_AUTH_SIGN = "LOG_INTERCEPT_SMALL_AUTH_SIGN";//密钥签名错误
    public static final String LOG_INTERCEPT_SMALL_AUTH_RSA = "LOG_INTERCEPT_SMALL_AUTH_RSA";//证书签名错误

    //SQL and XSS
    public static final String LOG_INTERCEPT_BIG_RULE = "LOG_INTERCEPT_BIG_RULE";// "规则拒绝日志"
    public static final String LOG_INTERCEPT_SMALL_RULE_SQL = "LOG_INTERCEPT_SMALL_RULE_SQL";  //疑似SQL注入
    public static final String LOG_INTERCEPT_SMALL_RULE_XSS = "LOG_INTERCEPT_SMALL_RULE_XSS";    //"疑似XSS攻击"
    public static final String LOG_INTERCEPT_SMALL_RULE_URLDECODE = "LOG_INTERCEPT_SMALL_RULE_URLDECODE"; //URLDECODE解码失败

    //监测日志
    public static final String LOG_MONITOR_BIG_SENSITIVE = "LOG_MONITOR_BIG_SENSITIVE";//敏感信息泄露
    public static final String LOG_MONITOR_SMALL_SENSITIVE_USERDATA = "LOG_MONITOR_SMALL_SENSITIVE_USERDATA";//敏感数据监测日志

    public static final String LOG_MONITOR_BIG_BEHAVIOR = "LOG_MONITOR_BIG_BEHAVIOR";//可疑行为
    public static final String LOG_MONITOR_SMALL_BEHAVIOR_SPECIAL = "LOG_MONITOR_SMALL_BEHAVIOR_SPECIAL";//特权账号监测日志
    public static final String LOG_MONITOR_SMALL_PWD = "LOG_MONITOR_SMALL_PWD";//密码明文传输


}
