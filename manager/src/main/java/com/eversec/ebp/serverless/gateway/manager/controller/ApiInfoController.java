package com.eversec.ebp.serverless.gateway.manager.controller;

import com.eversec.ebp.serverless.gateway.manager.dto.ApiAccessPolicyRelationDto;
import com.eversec.ebp.serverless.gateway.manager.dto.ApiInfoDto;
import com.eversec.ebp.serverless.gateway.manager.dto.ApiInfoQueryDto;
import com.eversec.ebp.serverless.gateway.dao.entity.ApiInfo;
import com.eversec.ebp.serverless.gateway.manager.service.ApiAccessService;
import com.eversec.ebp.serverless.gateway.manager.service.ApiInfoService;
import com.eversec.framework.core.exception.BusinessException;
import com.eversec.framework.core.reponse.Response;
import com.eversec.framework.core.reponse.ResponseCallBack;
import com.eversec.framework.core.reponse.ResponseCriteria;
import com.eversec.framework.core.response2.ResponseHolder;
import com.eversec.framework.core.response2.RestResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 * API资产表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-21
 */
@Api(tags = "API资产管理")
@RestController
@RequestMapping("/api/info")
public class ApiInfoController {

    private static final Logger LOGGER = LoggerFactory.getLogger(ApiInfoController.class);
    @Autowired
    private ApiInfoService apiInfoService;

    @Autowired
    private ApiAccessService apiAccessService;

    @ApiOperation("资产详情")
    @GetMapping(value = "/{id}")
    public RestResponse<ApiInfoDto> info(@PathVariable String id) {
        return ResponseHolder.build(() -> {
            ApiInfo apiInfo = apiInfoService.getById(id);
            if (apiInfo != null) {
                ApiInfoDto apiInfoDto = new ApiInfoDto(apiInfo);
                // FIXME 等访问策略功能完成，再加上
//            ApiAccess apiAccess = apiAccessService.getById(apiInfoDto.getAccessPolicyId());
//            apiInfoDto.setAccessPolicyName(Optional.ofNullable(apiAccess).map(ApiAccess::getName).orElse(""));
                return apiInfoDto;
            } else {
                throw new BusinessException(-1, "未找到资产");
            }
        });
    }

    @ApiOperation(value = "分页获取资产")
    @RequestMapping(path = "/page", method = RequestMethod.POST)
    public Response page(@RequestBody final ApiInfoQueryDto query) {
        return new ResponseCallBack() {
            @Override
            public void execute(ResponseCriteria criteria, Object... obj) {
                criteria.addSingleResult(apiInfoService.getPage(query));
            }
        }.sendRequest();
    }

    @ApiOperation(value = "新增资产")
    @RequestMapping(path = "/add", method = RequestMethod.POST)
    public RestResponse<ApiInfo> add(@RequestBody final ApiInfo apiInfo) {
        return ResponseHolder.build(() -> {
            final Integer add = apiInfoService.add(apiInfo);
            if (add == 1) {
                return apiInfo;
            } else {
                throw new BusinessException(-1, "新增失败");
            }
        });
    }

    @ApiOperation(value = "修改资产")
    @RequestMapping(path = "/update", method = RequestMethod.PUT)
    public RestResponse<Boolean> update(@RequestBody final ApiInfo apiInfo) {
        return ResponseHolder.build(() -> {
            final boolean update = apiInfoService.updateById(apiInfo);
            if (update) {
                return Boolean.TRUE;
            } else {
                throw new BusinessException(-1, "修改失败");
            }
        });
    }

    @ApiOperation("删除资产")
    @DeleteMapping(value = "{id}")
    public RestResponse<Boolean> del(@ApiParam("资产id") @PathVariable String id) {
        return ResponseHolder.build(() -> {
            final boolean del = apiInfoService.removeById(id);
            if (del) {
                return Boolean.TRUE;
            } else {
                throw new BusinessException(-1, "删除失败");
            }
        });

    }

    @ApiOperation("资产发布")
    @RequestMapping(path = "/publish/{id}", method = RequestMethod.PUT)
    public RestResponse<Boolean> publish(@ApiParam("资产id") @PathVariable String id) {
        return ResponseHolder.build(() -> {
            final int update = apiInfoService.publish(id);
            if (update == 1) {
                return Boolean.TRUE;
            } else {
                throw new BusinessException(-1, "发布失败");
            }
        });
    }

    @ApiOperation("资产取消发布")
    @RequestMapping(path = "/unpublish/{id}", method = RequestMethod.PUT)
    public RestResponse<Boolean> unpublish(@ApiParam("资产id") @PathVariable String id) {
        return ResponseHolder.build(() -> {
            final int update = apiInfoService.unpublish(id);
            if (update == 1) {
                return Boolean.TRUE;
            } else {
                throw new BusinessException(-1, "取消发布失败");
            }
        });
    }

    @ApiOperation("添加资产访问策略")
    @RequestMapping(path = "/addAccessPolicy", method = RequestMethod.PUT)
    public RestResponse<Boolean> addAccessPolicy(@ApiParam("资产和访问策略关联关系") @RequestBody ApiAccessPolicyRelationDto relationDto) {
        return ResponseHolder.build(() -> {
            final int update = apiInfoService.addAccessPolicy(relationDto.getApiId(), relationDto.getAccessPolicyId());
            if (update == 1) {
                return Boolean.TRUE;
            } else {
                throw new BusinessException(-1, "添加资产访问策略失败");
            }
        });
    }
}
