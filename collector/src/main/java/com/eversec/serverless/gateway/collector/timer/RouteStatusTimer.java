package com.eversec.serverless.gateway.collector.timer;

import com.eversec.serverless.gateway.collector.controller.RouteDefinitionService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;


/**
 * 类名称: TaskStatusScheduling
 * 类描述:任务调度
 * 修改时间: 2022 2023/10/13 11:31
 *
 * <AUTHOR>
 */
@Component
@Slf4j
public class RouteStatusTimer {

    @Autowired
    RouteDefinitionService routeDefinitionService;

    @Scheduled(fixedRate = 60000)
    public void doScheduling() {
        routeDefinitionService.reload();
    }

}



