package com.eversec.ebp.serverless.gateway.manager.bootstrap;

import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import org.springframework.boot.autoconfigure.jackson.Jackson2ObjectMapperBuilderCustomizer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * 配置类，用于自定义LocalDateTime的序列化格式。
 */
@Configuration
public class LocalDateTimeConfig {

    /**
     * 创建一个Bean，用于序列化LocalDateTime对象。
     * @return LocalDateTimeSerializer实例，用于将LocalDateTime序列化为指定格式的字符串。
     */
    @Bean
    public LocalDateTimeSerializer localDateTimeSerializer(){
        return new LocalDateTimeSerializer(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
    }

    /**
     * 创建一个Bean，用于自定义Jackson的ObjectMapper构建过程。
     * @return Jackson2ObjectMapperBuilderCustomizer实例，用于在构建ObjectMapper时应用LocalDateTimeSerializer。
     */
    @Bean
    public Jackson2ObjectMapperBuilderCustomizer jackson2ObjectMapperBuilderCustomizer(){
        return e -> e.serializerByType(LocalDateTime.class, localDateTimeSerializer());
    }

}
