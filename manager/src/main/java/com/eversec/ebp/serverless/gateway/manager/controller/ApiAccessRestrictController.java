package com.eversec.ebp.serverless.gateway.manager.controller;

import cn.hutool.core.util.StrUtil;
import com.eversec.ebp.serverless.gateway.dao.entity.ApiAccessRestrict;
import com.eversec.ebp.serverless.gateway.dao.entity.ApiInfo;
import com.eversec.ebp.serverless.gateway.manager.dto.ApiAccessRestrictDto;
import com.eversec.ebp.serverless.gateway.manager.dto.ApiAccessRestrictQueryDto;
import com.eversec.ebp.serverless.gateway.manager.service.ApiAccessRestrictService;
import com.eversec.ebp.serverless.gateway.manager.service.ApiInfoService;
import com.eversec.framework.core.exception.BusinessException;
import com.eversec.framework.core.reponse.Response;
import com.eversec.framework.core.reponse.ResponseCallBack;
import com.eversec.framework.core.reponse.ResponseCriteria;
import com.eversec.framework.core.response2.ResponseHolder;
import com.eversec.framework.core.response2.RestResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 * 封堵策略表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-21
 */
@Api(tags = "封堵策略管理")
@RestController
@RequestMapping("/api/access/restrict")
public class ApiAccessRestrictController {

    private static final Logger LOGGER = LoggerFactory.getLogger(ApiAccessRestrictController.class);

    @Autowired
    private ApiAccessRestrictService apiAccessRestrictService;

    @Autowired
    private ApiInfoService apiInfoService;

    @ApiOperation("封堵策略详情")
    @GetMapping(value = "/{id}")
    public RestResponse<ApiAccessRestrictDto> info(@PathVariable Long id) {
        return ResponseHolder.build(() -> {
            ApiAccessRestrict apiAccessRestrict = apiAccessRestrictService.getById(id);
            if (apiAccessRestrict != null) {
                // 设置资产名称
                if (apiAccessRestrict.getAssetId() > 0) {
                    ApiInfo apiInfo = apiInfoService.getById(apiAccessRestrict.getAssetId());
                    if (apiInfo != null) {
                        apiAccessRestrict.setAssetName(apiInfo.getName());
                    } else {
                        apiAccessRestrict.setAssetName("未知资产");
                    }
                } else {
                    apiAccessRestrict.setAssetName("未关联资产");
                }
                return new ApiAccessRestrictDto(apiAccessRestrict);
            } else {
                throw new BusinessException(-1, "未找到封堵策略");
            }
        });
    }

    @ApiOperation(value = "分页获取封堵策略")
    @RequestMapping(path = "/page", method = RequestMethod.POST)
    public Response page(@RequestBody final ApiAccessRestrictQueryDto query) {
        return new ResponseCallBack() {
            @Override
            public void execute(ResponseCriteria criteria, Object... obj) {
                criteria.addSingleResult(apiAccessRestrictService.getPage(query));
            }
        }.sendRequest();
    }

    @ApiOperation(value = "新增封堵策略")
    @RequestMapping(path = "/add", method = RequestMethod.POST)
    public RestResponse<ApiAccessRestrict> add(@RequestBody final ApiAccessRestrict apiAccessRestrict) {
        return ResponseHolder.build(() -> {
            final Integer add = apiAccessRestrictService.add(apiAccessRestrict);
            if (add == 1) {
                return apiAccessRestrict;
            } else {
                throw new BusinessException(-1, "新增失败");
            }
        });
    }

    @ApiOperation(value = "修改封堵策略")
    @RequestMapping(path = "/update", method = RequestMethod.PUT)
    public RestResponse<Boolean> update(@RequestBody final ApiAccessRestrict apiAccessRestrict) {
        return ResponseHolder.build(() -> {
            final boolean update = apiAccessRestrictService.updateById(apiAccessRestrict);
            if (update) {
                return Boolean.TRUE;
            } else {
                throw new BusinessException(-1, "修改失败");
            }
        });
    }

    @ApiOperation("删除封堵策略")
    @DeleteMapping(value = "{id}")
    public RestResponse<Boolean> del(@ApiParam("封堵策略id") @PathVariable Long id) {
        return ResponseHolder.build(() -> {
            final boolean del = apiAccessRestrictService.removeById(id);
            if (del) {
                return Boolean.TRUE;
            } else {
                throw new BusinessException(-1, "删除失败");
            }
        });
    }
}
