package com.eversec.ebp.serverless.gateway.manager.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.eversec.ebp.serverless.gateway.dao.entity.LogAccess;
import com.eversec.ebp.serverless.gateway.dao.mapper.LogAccessMapper;
import com.eversec.ebp.serverless.gateway.manager.service.LogAccessService;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 访问日志表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-22
 */
@Service
public class LogAccessServiceImpl extends ServiceImpl<LogAccessMapper, LogAccess> implements LogAccessService {

}
