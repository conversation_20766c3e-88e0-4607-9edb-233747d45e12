package com.eversec.ebp.serverless.gateway.manager.dto;

import com.eversec.ebp.serverless.gateway.manager.common.PageParams;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <p>
 * 封堵策略查询条件DTO
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-21
 */
@Data
public class ApiAccessRestrictQueryDto extends PageParams {

    @ApiModelProperty("限制类型：1-IP")
    private Integer restrictionType;

    @ApiModelProperty("动作：允许/拒绝")
    private String action;

    @ApiModelProperty("限制内容：一个IP或一个IP段")
    private String content;

    @ApiModelProperty("资产名称")
    private String assetName;
}
