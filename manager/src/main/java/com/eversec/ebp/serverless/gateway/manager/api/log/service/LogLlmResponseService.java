package com.eversec.ebp.serverless.gateway.manager.api.log.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.eversec.ebp.serverless.gateway.manager.api.log.entity.LogLlmResponse;
import org.springframework.http.codec.ServerSentEvent;
import reactor.core.publisher.Flux;

/**
 * <p>
 * 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-31
 */
public interface LogLlmResponseService extends IService<LogLlmResponse> {

	public Flux<ServerSentEvent<String>> getServerSentEventFluxDefault(String batchNo);

	public Flux<ServerSentEvent<String>> getServerSentEventFluxMY(String batchNo);

}
