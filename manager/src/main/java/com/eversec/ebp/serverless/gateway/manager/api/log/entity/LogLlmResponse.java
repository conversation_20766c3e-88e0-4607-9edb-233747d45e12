package com.eversec.ebp.serverless.gateway.manager.api.log.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 描述: 研判出来的告警事件<br>
 *
 * <AUTHOR>
 * @since 2025-03-31
 */
@Data
@TableName("log_llm_response")
public class LogLlmResponse {

	@TableField("batch_no")
	@ApiModelProperty(value = "批次号")
	private String batchNo;

	@TableField("llm_name")
	@ApiModelProperty(value = "大模型的名称")
	private String llmName;

	@TableField("query")
	@ApiModelProperty(value = "大模型的问题")
	private String query;

	@TableField("answer")
	@ApiModelProperty(value = "大模型的答案")
	private String answer;

	@TableField("response")
	@ApiModelProperty(value = "研判响应全文")
	private String response;

	@TableField("risk")
	@ApiModelProperty(value = "是否风险")
	private String risk;

	@TableField("risk_type")
	@ApiModelProperty(value = "风险类型")
	private String riskType;

	@TableField("action")
	@ApiModelProperty(value = "建议动作")
	private String action;

	@TableField("analysis")
	@ApiModelProperty(value = "分析")
	private String analysis;

	@TableField("confidence_level_1")
	@ApiModelProperty(value = "置信度-1用户意图")
	private Integer confidenceLevel1;

	@TableField("confidence_level_2")
	@ApiModelProperty(value = "置信度-1模型回答")
	private Integer confidenceLevel2;

	@TableField("create_time")
	@ApiModelProperty(value = "创建时间")
	private Long createTime;

}
