package com.eversec.ebp.serverless.gateway.manager.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.eversec.ebp.serverless.gateway.dao.entity.ApiAccess;
import com.eversec.ebp.serverless.gateway.manager.dto.ApiAccessQueryDto;
import com.eversec.framework.core.data.QueryResult;

/**
 * <p>
 * 访问控制表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-22
 */
public interface ApiAccessService extends IService<ApiAccess> {

    /**
     * 分页查询访问策略
     *
     * @param apiAccessQueryDto 查询条件
     * @return 分页结果
     */
    QueryResult<ApiAccess> getPage(ApiAccessQueryDto apiAccessQueryDto);

    /**
     * 新增访问策略
     *
     * @param apiAccess 访问策略信息
     * @return 影响行数
     */
    Integer add(ApiAccess apiAccess);
}
