package com.eversec.ebp.serverless.gateway.dao.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 访问日志研判结果
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-22
 */
@Getter
@Setter
@TableName("log_risk_assessment_results")
public class LogRiskAssessmentResults implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 日志批次号
     */
    @TableField("batch_no")
    private String batchNo;

    /**
     * 大模型问题
     */
    @TableField("query")
    private String query;

    /**
     * 大模型回答
     */
    @TableField("answer")
    private String answer;

    /**
     * 研判模型响应全文
     */
    @TableField("response")
    private String response;

    /**
     * 研判时间
     */
    @TableField("create_time")
    private Long createTime;

    /**
     * 风险类型
     */
    @TableField("risk_type")
    private String riskType;

    /**
     * 置信度-1用户意图
     */
    @TableField("confidence_level_1")
    private Integer confidenceLevel1;

    /**
     * 置信度-1模型回答
     */
    @TableField("confidence_level_2")
    private Integer confidenceLevel2;

    /**
     * 是否风险
     */
    @TableField("risk")
    private String risk;

    /**
     * 建议动作
     */
    @TableField("action")
    private String action;

    /**
     * 大模型的分析摘要
     */
    @TableField("analysis")
    private String analysis;

    /**
     * 研判大模型名称
     */
    @TableField("llm_name")
    private String llmName;
}
