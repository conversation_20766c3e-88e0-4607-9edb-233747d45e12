package com.eversec.ebp.serverless.gateway.dao.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

/**
 * 访问控制_黑白名单
 * @TableName api_access_restrict
 */
@TableName(value ="api_access_restrict")
@Data
public class ApiAccessRestrict implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id",type = IdType.AUTO)
    private Long id;

    /**
     * 限制类型1:IP
     */
    @TableField(value = "restriction_type")
    private Integer restrictionType;

    /**
     * 动作：0允许 1拒绝
     */
    @TableField(value = "action")
    private Integer action;

    /**
     * 一个ip或一个IP段
     */
    @TableField(value = "content")
    private String content;

    @TableField(value = "asset_id")
    private Long assetId;


    /**
     * 创建人
     */
    @TableField(value = "create_user")
    private String createUser;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private Long createTime;

    /**
     * 修改人
     */
    @TableField(value = "update_user")
    private String updateUser;

    /**
     * 修改时间
     */
    @TableField(value = "update_time")
    private Long updateTime;

    /**
     * 资产名称（非数据库字段，用于展示）
     */
    @TableField(exist = false)
    private String assetName;

}