package com.eversec.ebp.serverless.gateway.manager.service.impl;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.eversec.ebp.serverless.gateway.dao.entity.ApiAccessRestrict;
import com.eversec.ebp.serverless.gateway.dao.entity.ApiInfo;
import com.eversec.ebp.serverless.gateway.dao.mapper.ApiAccessRestrictMapper;
import com.eversec.ebp.serverless.gateway.manager.dto.ApiAccessRestrictQueryDto;
import com.eversec.ebp.serverless.gateway.manager.service.ApiAccessRestrictService;
import com.eversec.ebp.serverless.gateway.manager.service.ApiInfoService;
import com.eversec.framework.core.data.QueryResult;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collection;
import java.util.List;

/**
 * <p>
 * 访问控制_黑白名单 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-22
 */
@Service
public class ApiAccessRestrictServiceImpl extends ServiceImpl<ApiAccessRestrictMapper, ApiAccessRestrict> implements ApiAccessRestrictService {

    @Autowired
    private ApiAccessRestrictMapper apiAccessRestrictMapper;

    @Autowired
    private ApiInfoService apiInfoService;

    /**
     * 分页查询封堵策略
     *
     * @param apiAccessRestrictQueryDto 查询条件
     * @return 分页结果
     */
    @Override
    public QueryResult<ApiAccessRestrict> getPage(ApiAccessRestrictQueryDto apiAccessRestrictQueryDto) {
        // 设置分页信息 传递参数 页数  每页条数
        Page<ApiAccessRestrict> page = new Page<>(apiAccessRestrictQueryDto.getPageNum(), apiAccessRestrictQueryDto.getPageSize());
        // 查询条件
        Wrapper<ApiAccessRestrict> queryWrapper = getApiAccessRestrictQueryWrapper(apiAccessRestrictQueryDto);
        IPage<ApiAccessRestrict> restrictPageList = apiAccessRestrictMapper.selectPage(page, queryWrapper);
        // 获取查询数据
        List<ApiAccessRestrict> apiAccessRestrictList = restrictPageList.getRecords();

        // 为每个封堵策略设置资产名称(仅展示用)
        for (ApiAccessRestrict apiAccessRestrict : apiAccessRestrictList) {
            if (apiAccessRestrict.getAssetId() > 0) {
                // 通过资产ID获取资产信息
                ApiInfo apiInfo = apiInfoService.getById(apiAccessRestrict.getAssetId());
                if (apiInfo != null) {
                    apiAccessRestrict.setAssetName(apiInfo.getName());
                } else {
                    apiAccessRestrict.setAssetName("未知资产");
                }
            } else {
                apiAccessRestrict.setAssetName("未关联资产");
            }
        }

        QueryResult<ApiAccessRestrict> result = new QueryResult<>();
        result.setResultData(apiAccessRestrictList);
        result.setTotalRecord(restrictPageList.getTotal());
        return result;
    }

    /**
     * 构建查询条件
     *
     * @param condition 查询条件
     * @return 查询包装器
     */
    private Wrapper<ApiAccessRestrict> getApiAccessRestrictQueryWrapper(ApiAccessRestrictQueryDto condition) {
        QueryWrapper<ApiAccessRestrict> queryWrapper = new QueryWrapper<>();

        // 资产名称(模糊查询)查询条件
        if (StringUtils.isNotBlank(condition.getAssetName())) {
            // 先通过资产名称查询出资产ID列表
            QueryWrapper<ApiInfo> apiInfoQueryWrapper = new QueryWrapper<>();
            apiInfoQueryWrapper.like("name", condition.getAssetName());
            apiInfoQueryWrapper.eq("deleted", false);
            List<ApiInfo> apiInfoList = apiInfoService.list(apiInfoQueryWrapper);

            if (!apiInfoList.isEmpty()) {
                // 提取资产ID列表
                List<Long> assetIds = apiInfoList.stream().map(ApiInfo::getId).collect(java.util.stream.Collectors.toList());
                queryWrapper.in("asset_id", assetIds);
            } else {
                // 如果没有找到匹配的资产，则返回空结果
                queryWrapper.eq("id", -1); // 使用一个不存在的ID来确保返回空结果
            }
        }

        queryWrapper.orderByDesc("id");
        return queryWrapper;
    }

    /**
     * 新增封堵策略
     *
     * @param apiAccessRestrict 封堵策略信息
     * @return 影响行数
     */
    @Override
    @Transactional(propagation = Propagation.REQUIRED)
    public Integer add(ApiAccessRestrict apiAccessRestrict) {
        return apiAccessRestrictMapper.insert(apiAccessRestrict);
    }


    /**
     * 根据资产id列表获取封堵策略
     *
     * @param apiInfoIds 资产id列表
     * @return 资产列表
     */
    public List<ApiAccessRestrict> getApiAccessRestrictByApiInfoIds(Collection<Long> apiInfoIds) {
        QueryWrapper<ApiAccessRestrict> apiAccessRestrictQueryWrapper = new QueryWrapper<>();
        apiAccessRestrictQueryWrapper.in("asset_id", apiInfoIds);
        return apiAccessRestrictMapper.selectList(apiAccessRestrictQueryWrapper);
    }
}
