package com.eversec.ebp.serverless.gateway.manager.dto;

import com.eversec.ebp.serverless.gateway.manager.common.PageParams;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <p>
 * 访问策略查询条件DTO
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-21
 */
@Data
public class ApiAccessQueryDto extends PageParams {

    @ApiModelProperty("策略名称")
    private String name;

    @ApiModelProperty("是否支持跨域")
    private Boolean isCrossDomain;

    @ApiModelProperty("是否开启user-Agent")
    private Boolean isUserAgent;

    @ApiModelProperty("创建人")
    private String createUser;
}
