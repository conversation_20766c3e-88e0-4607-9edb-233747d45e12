package com.eversec.ebp.serverless.gateway.manager.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.eversec.ebp.serverless.gateway.dao.entity.LogRiskAssessmentResults;
import com.eversec.ebp.serverless.gateway.dao.mapper.LogRiskAssessmentResultsMapper;
import com.eversec.ebp.serverless.gateway.manager.service.LogRiskAssessmentResultsService;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 访问日志研判结果 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-22
 */
@Service
public class LogRiskAssessmentResultsServiceImpl extends ServiceImpl<LogRiskAssessmentResultsMapper, LogRiskAssessmentResults> implements LogRiskAssessmentResultsService {

}
