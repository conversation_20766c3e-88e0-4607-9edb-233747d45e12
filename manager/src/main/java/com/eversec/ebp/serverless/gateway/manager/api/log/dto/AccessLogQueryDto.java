package com.eversec.ebp.serverless.gateway.manager.api.log.dto;

import com.eversec.ebp.serverless.gateway.manager.common.PageParams;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class AccessLogQueryDto extends PageParams {

	@ApiModelProperty("请求开始时间")
	private Long reqStartTime;

	@ApiModelProperty("请求结束时间")
	private Long reqEndTime;

	@ApiModelProperty("请求头参数")
	private String reqHeaderParam;

	@ApiModelProperty("请求参数")
	private String reqParam;

	@ApiModelProperty("响应头参数")
	private String resHeaderParam;

	@ApiModelProperty("响应参数")
	private String resParam;

	@ApiModelProperty("路由主键")
	private String routeId;

	@ApiModelProperty("按请求时间排序")
	private Integer ascDesc = 0;

	@ApiModelProperty("消息主键")
	private String id;

	@ApiModelProperty("响应码")
	private Integer statusCode;

}
