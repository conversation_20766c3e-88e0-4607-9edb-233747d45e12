package com.eversec.ebp.serverless.gateway.manager.dto;

import com.eversec.ebp.serverless.gateway.dao.entity.ApiInfo;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 * API资产表
 * </p>
 *
 * <AUTHOR> name
 * @since 2025-05-21
 */
@Getter
@Setter
public class ApiInfoExtraDto extends ApiInfoDto implements Serializable {

    // 访问策略
    private ApiAccessDto apiAccess;

    // 封堵策略列表
    private List<ApiAccessRestrictDto> ApiAccessRestrictList = new ArrayList<>();

    public ApiInfoExtraDto() {
    }

    public ApiInfoExtraDto(ApiInfo apiInfo) {
        // apiinfo 全部字段赋值ApiInfoDto
        this.setId(apiInfo.getId());
        this.setName(apiInfo.getName());
        this.setInterfaceAddress(apiInfo.getInterfaceAddress());
        this.setProxyPath(apiInfo.getProxyPath());
        this.setAccessPolicyId(apiInfo.getAccessPolicyId());
        this.setProxyAddress(apiInfo.getProxyAddress());
        this.setPublishStatus(apiInfo.getPublishStatus());
        this.setCreateTime(apiInfo.getCreateTime());
        this.setCreateUser(apiInfo.getCreateUser());
        this.setUpdateTime(apiInfo.getUpdateTime());
        this.setUpdateUser(apiInfo.getUpdateUser());
    }
}
