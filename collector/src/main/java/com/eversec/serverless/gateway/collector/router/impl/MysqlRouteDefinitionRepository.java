package com.eversec.serverless.gateway.collector.router.impl;


import com.alibaba.fastjson.JSONObject;
import com.eversec.ebp.serverless.gateway.dao.entity.ApiInfo;
import com.eversec.serverless.gateway.collector.router.CustomRouteDefinitionRepository;
import java.net.URI;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.gateway.handler.predicate.PredicateDefinition;
import org.springframework.cloud.gateway.route.RouteDefinition;
import org.springframework.stereotype.Component;
import org.springframework.web.util.UriComponentsBuilder;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;


@Component
@Slf4j
public class MysqlRouteDefinitionRepository implements CustomRouteDefinitionRepository {

    @Override
    public Flux<RouteDefinition> getRouteDefinitions() {
        return Flux.fromIterable(ROUTES.values());
    }

    @Override
    public Mono<Void> save(Mono<RouteDefinition> route) {
        return route.flatMap(rd -> {
            String value = JSONObject.toJSONString(rd);
            log.debug("新增路由：{}", value);
            ROUTES.put(rd.getId(), rd);
            return Mono.empty();
        });
    }

    @Override
    public Mono<Void> delete(Mono<String> routeId) {
        String id = routeId.block();
        log.debug("移除路由：{}", id);
        ROUTES.remove(id);
        return Mono.empty();
    }


    @Override
    public void reload() {
        //todo http请求平台 从平台拉取路由信息，刷新路由表
        List<ApiInfo> apiInfoList = null;
        //已下线的路由处理
        offLineRoutes(apiInfoList);

        //2) 新增或刷新路由
        apiInfoList.stream().forEach(value -> {
            log.debug("加载路由：{}", value);
            RouteDefinition rd = apiInfoChangeRoutes(value);
            ROUTES.put(rd.getId(), rd);
        });
    }

    public static void offLineRoutes(List<ApiInfo> apiInfoList) {
        final List<Long> collect = apiInfoList.stream().map(ApiInfo::getId).collect(Collectors.toList());
        final Set<String> currentRoutes = ROUTES.keySet();
        // 移除不在 collect 集合中的数据
        for (String routeId : currentRoutes) {
            Long id = Long.valueOf(routeId);
            if (!collect.contains(id)) {
                ROUTES.remove(id);
            }
        }
    }


    public RouteDefinition apiInfoChangeRoutes(ApiInfo apiInfo) {
        RouteDefinition routeDefinition = new RouteDefinition();

        // 设置路由ID（使用API的ID作为路由ID）
        routeDefinition.setId(String.valueOf(apiInfo.getId()));

        // 设置目标URI
        URI url = null;
        if (apiInfo.getInterfaceAddress() != null && apiInfo.getInterfaceAddress().startsWith("http")) {
            url = UriComponentsBuilder.fromHttpUrl(apiInfo.getInterfaceAddress()).build().toUri();
        } else {
            try {
                url = new URI(apiInfo.getInterfaceAddress());
            } catch (Exception e) {
                log.info("url转换异常", e);
                return null;
            }
        }
        routeDefinition.setUri(url);

        // 设置断言（基于代理路径）
        List<PredicateDefinition> predicateDefinitions = new ArrayList<>();

        // 请求路径断言
        PredicateDefinition pathPredicate = new PredicateDefinition();
        pathPredicate.setName("Path");
        pathPredicate.addArg("pattern", apiInfo.getProxyPath());
        predicateDefinitions.add(pathPredicate);
        routeDefinition.setPredicates(predicateDefinitions);
        routeDefinition.setOrder(0);
        return routeDefinition;
    }
}
