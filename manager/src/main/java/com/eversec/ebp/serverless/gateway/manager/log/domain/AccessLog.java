package com.eversec.ebp.serverless.gateway.manager.log.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;

import java.util.HashSet;
import java.util.Set;

/**
 * 类名称: AccessLog<br>
 * 类描述: 访问日志<br>
 * 修改时间: 2021年9月20日<br>
 *
 * <AUTHOR>
 */
@TableName("log_access")
@Data
public class AccessLog {

    public static final String GATEWAY_CONTEXT_ACCESS_LOG = "GATEWAY_CONTEXT_ACCESS_LOG";

    /**
     * 日志的流水ID，用于做信息核对
     */
    @TableId("id")
    private String id;

    /**
     * 日志的批次号，http协议存在text-event类型的数据
     * 次类型的数据会出现一次请求多次响应的问题
     * 需要同一个批次号来关联多次响应
     */
    @TableField("batch_no")
    private String batchNo;

    /**
     * 日志类型
     */
    @TableField("log_type")
    private LogType logType;

    /**
     * 日志命中的规则ID（阻断日志和访问日志没有规则ID）
     */
    @TableField("rule_id")
    private String ruleId;

    /**
     * 规则大类
     */
    @TableField("main_type")
    private String mainType;

    /**
     * 规则小类
     */
    @TableField("sub_type")
    private String subType;
    
    /**
     * 日志的标签
     */
    @TableField("tags")
    private String tags;
    @TableField(exist = false)
    private Set<String> tagSet;

    /**
     * 协议
     */
    @TableField("protocol")
    private String protocol;

    /**
     * 请求地址
     */
    @TableField("request_url")
    private String requestUrl;

    /**
     * 请求路径
     */
    @TableField("request_path")
    private String requestPath;

    /**
     * 请求方法
     */
    @TableField("request_method")
    private String requestMethod;

    /**
     * 请求类型
     */
    @TableField("request_media_type")
    private String requestMediaType;

    /**
     * 请求头
     */
    @TableField("request_header")
    private String requestHeader;

    /**
     * 请求查询参数
     */
    @TableField("request_query_params")
    private String requestQueryParams;

    /**
     * 请求体的大小
     */
    @TableField("request_content_length")
    private Long requestContentLength;

    /**
     * 请求体
     */
    @TableField("request_body")
    private String requestBody;

    /**
     * 请求ip
     */
    @TableField("request_ip")
    private String requestIp;

    /**
     * 请求时间
     */
    @TableField("request_time")
    private Long requestTime;

    /**
     * 访问实例
     */
    @TableField("target_server")
    private String targetServer;

    /**
     * 响应状态码
     */
    @TableField("response_status")
    private Integer responseStatus;

    /**
     * 响应类型
     */
    @TableField("response_media_type")
    private String responseMediaType;

    /**
     * 响应头
     */
    @TableField("response_header")
    private String responseHeader;

    /**
     * 响应体的大小
     */
    @TableField("response_content_length")
    private Long responseContentLength;

    /**
     * 响应体（认证失败时候，此处为认证失败的提示信息）
     */
    @TableField("response_data")
    private String responseData;

    //根据多条访问日志合并出来的响应体，在http协议存在text-event类型的数据，会有多个响应体，该字段就是将多个响应体合并在一起
    @TableField(exist = false)
    private String allRspData;

    //批次记录数量（非数据库字段，用于显示该批次包含的日志条数）
    @TableField(exist = false)
    private Integer batchRecordCount;

    /**
     * 响应时间
     */
    @TableField("response_time")
    private Long responseTime;

    /**
     * 执行时间
     */
    @TableField("execute_time")
    private Long executeTime;

    /**
     * 路由ID
     */
    @TableField("route_id")
    private String routeId;

    /**
     * 路由转发地址
     */
    @TableField("route_uri")
    private String routeUri;

    /**
     * 路由完整信息
     */
    @TableField("route")
    private String route;

    
    public void addTag(String tag) {
    	if(CollectionUtils.isEmpty(this.tagSet)) {
            this.tagSet = new HashSet<>();
    	}
    	this.tagSet.add(tag);
    	this.tags = StringUtils.join(this.tagSet, ",");
    }
}
