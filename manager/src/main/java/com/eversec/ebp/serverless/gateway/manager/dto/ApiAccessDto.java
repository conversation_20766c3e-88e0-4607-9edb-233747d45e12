package com.eversec.ebp.serverless.gateway.manager.dto;

import com.eversec.ebp.serverless.gateway.dao.entity.ApiAccess;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * <p>
 * 访问策略数据传输对象
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-21
 */
@Getter
@Setter
public class ApiAccessDto implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("策略ID")
    private Integer id;

    @ApiModelProperty("策略名称")
    private String name;

    @ApiModelProperty("支持跨域")
    private Boolean isCrossDomain;

    @ApiModelProperty("是否启用")
    private Boolean isActive;

    @ApiModelProperty("指定user-agent限制")
    private String userAgent;

    @ApiModelProperty("创建人")
    private String createUser;

    @ApiModelProperty("创建时间")
    private Long createTime;

    @ApiModelProperty("修改人")
    private String updateUser;

    @ApiModelProperty("修改时间")
    private Long updateTime;

    @ApiModelProperty("防护资产数量")
    private Integer accessAssetCount;

    public ApiAccessDto() {
    }

    /**
     * 从ApiAccess实体构造DTO
     *
     * @param apiAccess 访问策略实体
     */
    public ApiAccessDto(ApiAccess apiAccess) {
        this.id = apiAccess.getId();
        this.name = apiAccess.getName();
        this.isCrossDomain = apiAccess.getIsCrossDomain();
        this.isActive = apiAccess.getIsActive();
        this.userAgent = apiAccess.getUserAgent();
        this.createUser = apiAccess.getCreateUser();
        this.createTime = apiAccess.getCreateTime();
        this.updateUser = apiAccess.getUpdateUser();
        this.updateTime = apiAccess.getUpdateTime();
    }
}
