package com.eversec.serverless.gateway.collector.controller;

import com.eversec.serverless.gateway.collector.router.CustomRouteDefinitionRepository;
import java.util.ArrayList;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.gateway.event.RefreshRoutesEvent;
import org.springframework.cloud.gateway.route.RouteDefinition;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.context.ApplicationEventPublisherAware;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

/**
 * 类名称: RouteDefinitionService<br>
 * 类描述: 路由的管理服务<br>
 * 修改时间: 2025年5月27日<br>
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class RouteDefinitionService implements ApplicationEventPublisherAware {

    @Autowired
    private CustomRouteDefinitionRepository routeDefinitionRepository;

    @Autowired
    private ApplicationEventPublisher applicationEventPublisher;


    @Override
    public void setApplicationEventPublisher(ApplicationEventPublisher applicationEventPublisher) {
        this.applicationEventPublisher = applicationEventPublisher;
    }

    public void reload() {
        routeDefinitionRepository.reload();
        applicationEventPublisher.publishEvent(new RefreshRoutesEvent(this));
    }

    public List<RouteDefinition> all() {
        List<RouteDefinition> rds = new ArrayList<>();
        routeDefinitionRepository.getRouteDefinitions().subscribe(r -> {
            rds.add(r);
        });
        return rds;
    }

    public void add(RouteDefinition rd) {
        routeDefinitionRepository.save(Mono.just(rd)).subscribe();
        applicationEventPublisher.publishEvent(new RefreshRoutesEvent(this));
    }

    public void update(RouteDefinition rd) {
        routeDefinitionRepository.delete(Mono.just(rd.getId()));
        routeDefinitionRepository.save(Mono.just(rd)).subscribe();
        applicationEventPublisher.publishEvent(new RefreshRoutesEvent(this));
    }

    public void delete(String id) {
        routeDefinitionRepository.delete(Mono.just(id));
        applicationEventPublisher.publishEvent(new RefreshRoutesEvent(this));
    }


}
