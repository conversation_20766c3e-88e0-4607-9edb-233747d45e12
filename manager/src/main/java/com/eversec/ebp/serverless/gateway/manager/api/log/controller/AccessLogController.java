package com.eversec.ebp.serverless.gateway.manager.api.log.controller;

import com.eversec.ebp.serverless.gateway.manager.api.log.dto.AccessLogQueryDto;
import com.eversec.ebp.serverless.gateway.manager.api.log.service.AccessLogInfoService;
import com.eversec.ebp.serverless.gateway.manager.log.domain.AccessLog;
import com.eversec.framework.core.reponse.Response;
import com.eversec.framework.core.reponse.ResponseCallBack;
import com.eversec.framework.core.reponse.ResponseCriteria;
import com.eversec.framework.core.response2.ResponseHolder;
import com.eversec.framework.core.response2.RestResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@Api(tags = "网关日志")
@RestController
@RequestMapping("/api/log")
public class AccessLogController {

	@Autowired
	private AccessLogInfoService accessLogInfoService;

	@ApiOperation(value = "分页获取网关日志（按批次号聚合，避免重复展示）")
	@RequestMapping(path = "/page", method = RequestMethod.POST)
	public Response page(@RequestBody final AccessLogQueryDto query) {
		return new ResponseCallBack() {
			@Override
			public void execute(ResponseCriteria criteria, Object... obj) {
				// 使用按批次号聚合的分页查询，避免同一批次号重复展示
				criteria.addSingleResult(accessLogInfoService.getApiAccessLogPageListGroupByBatch(query));
			}
		}.sendRequest();
	}

	@ApiOperation(value = "分页获取网关日志（原始数据，不聚合）")
	@RequestMapping(path = "/page/raw", method = RequestMethod.POST)
	public Response pageRaw(@RequestBody final AccessLogQueryDto query) {
		return new ResponseCallBack() {
			@Override
			public void execute(ResponseCriteria criteria, Object... obj) {
				// 原始的分页查询，不进行批次号聚合
				criteria.addSingleResult(accessLogInfoService.getApiAccessLogPageList(query));
			}
		}.sendRequest();
	}

	@ApiOperation(value = "查询一个批次下日志内容聚合-针对大模型对话日志(响应类型为streaming)")
	@RequestMapping(path = "/{batchNo}", method = RequestMethod.POST)
	public RestResponse<AccessLog> page(@PathVariable String batchNo) {
		return ResponseHolder.build(() -> accessLogInfoService.getLogAggregation(batchNo));
	}

	@ApiOperation(value = "查询同一批次日志ID列表")
	@RequestMapping(path = "/getLogIdList", method = RequestMethod.GET)
	public RestResponse<List<String>> pageID(@RequestParam String batchNo) {
		return ResponseHolder.build(() -> accessLogInfoService.getIdListByBatchNo(batchNo));
	}

	@ApiOperation(value = "获取一条日志的详情")
	@RequestMapping(path = "/getOneById", method = RequestMethod.GET)
	public RestResponse<AccessLog> oneLog(@RequestParam String logId) {
		return ResponseHolder.build(() -> accessLogInfoService.getOneByAccessLogId(logId));
	}
}
