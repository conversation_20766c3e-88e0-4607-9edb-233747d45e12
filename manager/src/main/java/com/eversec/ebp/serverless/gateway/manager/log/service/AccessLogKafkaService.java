package com.eversec.ebp.serverless.gateway.manager.log.service;

import com.alibaba.fastjson.JSONObject;
import com.eversec.ebp.serverless.gateway.manager.log.domain.AccessLog;
import org.apache.kafka.clients.producer.KafkaProducer;
import org.apache.kafka.clients.producer.ProducerRecord;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnExpression;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 类名称: AccessLogKafkaService<br>
 * 类描述: 日志通过kafka的输出通道<br>
 * 修改时间: 2021年9月20日<br>
 * <AUTHOR>
 */
@Service("AccessLogKafkaService")
@ConditionalOnExpression("${gateway.log.output.kafka.enable:false}==true")
public class AccessLogKafkaService implements AccessLogService {
	
	private static Logger logger = LoggerFactory.getLogger(AccessLogKafkaService.class);
	
	@Value("${gateway.log.output.kafka.topic:ACCESS_LOG}")
	private String topic;
	
	@Autowired
	KafkaProducer<String, String> kafkaProducer;

	@Override
	public void output(List<AccessLog> accessLogs) {
		for (AccessLog accessLog: accessLogs) {
			String key = accessLog.getRouteId() + ":" + accessLog.getRequestIp();
			String value = JSONObject.toJSONString(accessLog);
			ProducerRecord<String, String> record = new ProducerRecord<String, String>(topic, key, value);
			kafkaProducer.send(record);
			logger.debug("日志输出到kafka，消息id：{}，topic：{}", accessLog.getId(), topic);
		}
	}
	
}
