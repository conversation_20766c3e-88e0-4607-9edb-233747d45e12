package com.eversec.serverless.gateway.collector.controller;

import com.eversec.framework.core.response2.ResponseHolder;
import com.eversec.framework.core.response2.RestResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.gateway.route.RouteDefinition;
import org.springframework.cloud.gateway.route.RouteLocator;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 类名称: RouteDefinitionController<br>
 * 类描述: 路由管理API<br>
 * 修改时间: 2021年9月19日<br>
 * <AUTHOR>
 */
@Api(tags = "路由管理")
@RestController()
@RequestMapping("/router")
public class RouteDefinitionController {
	
	@Autowired
	private RouteDefinitionService definitionService;

	@ApiOperation(value="查询路由")
	@GetMapping
	public RestResponse<List<RouteDefinition>> all() {
		return ResponseHolder.build(() -> {
			return definitionService.all();
		});
	} 
	
	@ApiOperation(value="增加路由")
	@PostMapping
	public RestResponse<Boolean> add(@RequestBody RouteDefinition rd) {
		return ResponseHolder.build(() -> {
			definitionService.add(rd);
			return Boolean.TRUE;
		});
	} 
	
	@ApiOperation(value="修改路由")
	@PutMapping
	public RestResponse<Boolean> update(@RequestBody RouteDefinition rd) {
		return ResponseHolder.build(() -> {
			definitionService.update(rd);
			return Boolean.TRUE;
		});
	} 

	@ApiOperation(value="删除路由")
	@DeleteMapping("{id}")
	public RestResponse<Boolean> delete(@PathVariable String id) {
		return ResponseHolder.build(() -> {
			definitionService.delete(id);
			return Boolean.TRUE;
		});
	}
}
