package com.eversec.ebp.serverless.gateway.manager.service.impl;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.eversec.ebp.serverless.gateway.dao.dto.ApiPublishStatus;
import com.eversec.ebp.serverless.gateway.dao.entity.ApiInfo;
import com.eversec.ebp.serverless.gateway.dao.mapper.ApiInfoMapper;
import com.eversec.ebp.serverless.gateway.manager.dto.ApiInfoQueryDto;
import com.eversec.ebp.serverless.gateway.manager.service.ApiInfoService;
import com.eversec.framework.core.data.QueryResult;
import java.util.List;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

/**
 * <p>
 * API资产表 服务实现类
 * </p>
 *
 * <AUTHOR> name
 * @since 2025-05-21
 */
@Service
@Transactional(readOnly = true)
public class ApiInfoServiceImpl extends ServiceImpl<ApiInfoMapper, ApiInfo> implements ApiInfoService {


    @Autowired
    private ApiInfoMapper apiInfoMapper;

    /**
     * 分页查询应用
     *
     * @param apiInfoQueryDto 查询条件
     * @return
     */
    public QueryResult<ApiInfo> getPage(ApiInfoQueryDto apiInfoQueryDto) {
        //设置分页信息 传递参数 页数  每页条数
        Page<ApiInfo> page = new Page<>(apiInfoQueryDto.getPageNum(), apiInfoQueryDto.getPageSize());
        //查询条件
        Wrapper<ApiInfo> queryWrapper = getAppInfoQueryWrapper(apiInfoQueryDto);
        IPage<ApiInfo> infoPageList = apiInfoMapper.selectPage(page, queryWrapper);
        //获取查询数据
        List<ApiInfo> apiInfoList = infoPageList.getRecords();
        QueryResult<ApiInfo> result = new QueryResult<>();
        result.setResultData(apiInfoList);
        result.setTotalRecord(infoPageList.getTotal());
        return result;
    }

    private Wrapper<ApiInfo> getAppInfoQueryWrapper(ApiInfoQueryDto condition) {
        QueryWrapper<ApiInfo> appWrapper = new QueryWrapper<>();

        if (StringUtils.isNotBlank(condition.getName())) {
            appWrapper.like("name", condition.getName());
        }

        appWrapper.orderByDesc("create_time");
        return appWrapper;
    }

    /**
     * 新增资产
     *
     * @param apiInfo 资产信息
     * @return
     */
    @Transactional(readOnly = false, propagation = Propagation.REQUIRED)
    public Integer add(ApiInfo apiInfo) {
        // TODO 生成proxy_address
        return apiInfoMapper.insert(apiInfo);

    }

    /**
     * 发布资产
     *
     * @param id 资产id
     * @return
     */
    @Transactional(readOnly = false, propagation = Propagation.REQUIRED)
    public Integer publish(String id) {
        ApiInfo apiInfo = apiInfoMapper.selectById(id);
        if (apiInfo.getId() == null) {
            return -1;
        }
        apiInfo.setPublishStatus(ApiPublishStatus.ONLINE);
        return apiInfoMapper.updateById(apiInfo);
    }

    /**
     * 发布资产
     *
     * @param id 资产id
     * @return
     */
    @Transactional(readOnly = false, propagation = Propagation.REQUIRED)
    public Integer unpublish(String id) {
        ApiInfo apiInfo = apiInfoMapper.selectById(id);
        if (apiInfo.getId() == null) {
            return -1;
        }
        apiInfo.setPublishStatus(ApiPublishStatus.OFFLINE);
        return apiInfoMapper.updateById(apiInfo);
    }


    /**
     * 添加资产访问策略
     *
     * @param id             资产id
     * @param accessPolicyId 资产访问策略id
     * @return
     */
    @Override
    @Transactional(readOnly = false, propagation = Propagation.REQUIRED)
    public Integer addAccessPolicy(String id, String accessPolicyId) {
        ApiInfo apiInfo = apiInfoMapper.selectById(id);
        if (apiInfo == null || apiInfo.getId() == null) {
            return -1;
        }
        apiInfo.setAccessPolicyId(accessPolicyId);
        return apiInfoMapper.updateById(apiInfo);
    }

    /**
     * 根据访问策略ID统计防护资产数量
     *
     * @param accessPolicyId 访问策略ID
     * @return 防护资产数量
     */
    @Override
    public Integer countByAccessPolicyId(String accessPolicyId) {
        if (StringUtils.isBlank(accessPolicyId)) {
            return 0;
        }
        QueryWrapper<ApiInfo> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("access_policy_id", accessPolicyId);
        queryWrapper.eq("deleted", false); // 排除已删除的记录
        return Math.toIntExact(apiInfoMapper.selectCount(queryWrapper));
    }
}
