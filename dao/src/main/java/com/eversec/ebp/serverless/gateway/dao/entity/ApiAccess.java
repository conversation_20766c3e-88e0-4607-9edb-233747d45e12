package com.eversec.ebp.serverless.gateway.dao.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

/**
 * 访问控制表
 * @TableName api_access
 */
@TableName(value ="api_access")
@Data
public class ApiAccess implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id",type = IdType.AUTO)
    private Long id;

    /**
     * 策略名称
     */
    @TableField(value = "name")
    private String name;

    /**
     * 支持跨域
     */
    @TableField(value = "is_cross_domain")
    private Boolean isCrossDomain;

    /**
     * 是否启用
     */
    @TableField(value = "is_active")
    private Boolean isActive;

    /**
     * 指定user-agent限制：
     */
    @TableField(value = "user_agent")
    private String userAgent;

    /**
     * 允许访问的时间段
     */
    @TableField(value = "access_time")
    private String accessTime;

    /**
     * 创建人
     */
    @TableField(value = "create_user")
    private String createUser;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private Long createTime;

    /**
     * 修改人
     */
    @TableField(value = "update_user")
    private String updateUser;

    /**
     * 修改时间
     */
    @TableField(value = "update_time")
    private Long updateTime;

    /**
     * 防护资产数量（非数据库字段，用于展示）
     */
    @TableField(exist = false)
    private Integer accessAssetCount;

}