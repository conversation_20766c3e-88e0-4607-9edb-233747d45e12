package com.eversec.ebp.serverless.gateway.manager.log.service;

import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang.time.DateFormatUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.IOException;
import java.util.Date;

/**
 * 类名称: SampleService<br>
 * 类描述: 样本还原service<br>
 * 修改时间: 2021年11月21日<br>
 * <AUTHOR>
 */
@Service
public class SampleService {
	
	@Value("${gateway.log.output.sample.dir:${user.dir}/logs/sample}")
	private String dir;

    /**
     * 输出样本文件
     * @param route
     * @param ext
     * @param body
     * @return
     */
	public String wirteFile(String route, String ext, byte[] body) {
		String md5 = DigestUtils.md5Hex(body);
		String filename = md5 + "." + ext;
		
		String hh = DateFormatUtils.format(new Date(), "yyyyMMddHH");
		String path = dir + File.separator + route + File.separator + hh;
		File dir = new File(path);
		if(!dir.exists()) {
			dir.mkdirs();
		}
		
		String file = path + File.separator + filename;
		File out = new File(file);
		try {
			FileUtils.writeByteArrayToFile(out, body);
		} catch (IOException e) {
			e.printStackTrace();
		}
		return filename;
	}

}
