package com.eversec.ebp.serverless.gateway.manager.controller;

import com.eversec.ebp.serverless.gateway.dao.entity.ApiAccess;
import com.eversec.ebp.serverless.gateway.manager.dto.ApiAccessDto;
import com.eversec.ebp.serverless.gateway.manager.dto.ApiAccessQueryDto;
import com.eversec.ebp.serverless.gateway.manager.service.ApiAccessService;
import com.eversec.ebp.serverless.gateway.manager.service.ApiInfoService;
import com.eversec.framework.core.exception.BusinessException;
import com.eversec.framework.core.reponse.Response;
import com.eversec.framework.core.reponse.ResponseCallBack;
import com.eversec.framework.core.reponse.ResponseCriteria;
import com.eversec.framework.core.response2.ResponseHolder;
import com.eversec.framework.core.response2.RestResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 * 访问策略表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-21
 */
@Api(tags = "访问策略管理")
@RestController
@RequestMapping("/api/access")
public class ApiAccessController {

    private static final Logger LOGGER = LoggerFactory.getLogger(ApiAccessController.class);

    @Autowired
    private ApiAccessService apiAccessService;

    @ApiOperation("访问策略详情")
    @GetMapping(value = "/{id}")
    public RestResponse<ApiAccessDto> info(@PathVariable String id) {
        return ResponseHolder.build(() -> {
            ApiAccess apiAccess = apiAccessService.getById(id);
            if (apiAccess != null) {
	            return new ApiAccessDto(apiAccess);
            } else {
                throw new BusinessException(-1, "未找到访问策略");
            }
        });
    }

    @ApiOperation(value = "分页获取访问策略")
    @RequestMapping(path = "/page", method = RequestMethod.POST)
    public Response page(@RequestBody final ApiAccessQueryDto query) {
        return new ResponseCallBack() {
            @Override
            public void execute(ResponseCriteria criteria, Object... obj) {
                criteria.addSingleResult(apiAccessService.getPage(query));
            }
        }.sendRequest();
    }

    @ApiOperation(value = "新增访问策略")
    @RequestMapping(path = "/add", method = RequestMethod.POST)
    public RestResponse<ApiAccess> add(@RequestBody final ApiAccess apiAccess) {
        return ResponseHolder.build(() -> {
            final Integer add = apiAccessService.add(apiAccess);
            if (add == 1) {
                return apiAccess;
            } else {
                throw new BusinessException(-1, "新增失败");
            }
        });
    }

    @ApiOperation(value = "修改访问策略")
    @RequestMapping(path = "/update", method = RequestMethod.PUT)
    public RestResponse<Boolean> update(@RequestBody final ApiAccess apiAccess) {
        return ResponseHolder.build(() -> {
            final boolean update = apiAccessService.updateById(apiAccess);
            if (update) {
                return Boolean.TRUE;
            } else {
                throw new BusinessException(-1, "修改失败");
            }
        });
    }

    @ApiOperation("删除访问策略")
    @DeleteMapping(value = "{id}")
    public RestResponse<Boolean> del(@ApiParam("访问策略id") @PathVariable String id) {
        return ResponseHolder.build(() -> {
            final boolean del = apiAccessService.removeById(id);
            if (del) {
                return Boolean.TRUE;
            } else {
                throw new BusinessException(-1, "删除失败");
            }
        });
    }
}