package com.eversec.ebp.serverless.gateway.dao.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

/**
 * 访问控制_黑白名单
 * @TableName api_access_restrict
 */
@TableName(value ="api_access_restrict")
@Data
public class ApiAccessRestrict implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id")
    private Integer id;

    /**
     * 限制类型1:IP
     */
    @TableField(value = "restriction_type")
    private Integer restrictionType;

    /**
     * 动作：0允许 1拒绝
     */
    @TableField(value = "action")
    private String action;

    /**
     * 一个ip或一个IP段
     */
    @TableField(value = "content")
    private String content;

    @TableField(value = "asset_id")
    private String assetId;


    /**
     * 创建人
     */
    @TableField(value = "create_user")
    private String createUser;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private Long createTime;

    /**
     * 修改人
     */
    @TableField(value = "update_user")
    private String updateUser;

    /**
     * 修改时间
     */
    @TableField(value = "update_time")
    private Long updateTime;

}