package com.eversec.ebp.serverless.gateway.manager.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.eversec.ebp.serverless.gateway.dao.entity.ApiAccess;
import com.eversec.ebp.serverless.gateway.dao.mapper.ApiAccessMapper;
import com.eversec.ebp.serverless.gateway.manager.service.ApiAccessService;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 访问控制表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-22
 */
@Service
public class ApiAccessServiceImpl extends ServiceImpl<ApiAccessMapper, ApiAccess> implements ApiAccessService {

}
