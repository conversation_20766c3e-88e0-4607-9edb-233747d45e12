
package com.eversec.ebp.serverless.gateway.manager.log.service;

import org.apache.kafka.clients.producer.KafkaProducer;
import org.apache.kafka.clients.producer.ProducerConfig;
import org.apache.kafka.common.serialization.StringSerializer;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnExpression;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.kafka.annotation.EnableKafka;

import java.util.Properties;

@Configuration
@EnableKafka
@ConditionalOnExpression("${gateway.log.output.kafka.enable:false}==true")
public class KafkaProducerConfig {
	
	private static Logger logger = LoggerFactory.getLogger(KafkaProducerConfig.class);

	@Value("${gateway.log.output.kafka.bootstrap-servers}")
	private String kafkaServers;

	@Bean
	public KafkaProducer<String, String> kafkaProducer() {
		Properties properties = properties();
		logger.debug("初始化kafka，参数信息：{}", properties.toString());
		return new KafkaProducer<String, String>(properties());
	}

	public Properties properties() {
		Properties props = new Properties();
		props.put(ProducerConfig.BOOTSTRAP_SERVERS_CONFIG, kafkaServers);
		props.put(ProducerConfig.BUFFER_MEMORY_CONFIG, 20000000);// 20M 消息缓存
		// 生产者空间不足时，send()被阻塞的时间，默认60s
		props.put(ProducerConfig.MAX_BLOCK_MS_CONFIG, 6000);
		// 生产者重试次数
		props.put(ProducerConfig.RETRIES_CONFIG, 2);
		// 指定ProducerBatch（消息累加器中BufferPool中的）可复用大小(1M)
		props.put(ProducerConfig.BATCH_SIZE_CONFIG, 1048576);
		// 生产者会在ProducerBatch被填满或者等待超过LINGER_MS_CONFIG时发送
		props.put(ProducerConfig.LINGER_MS_CONFIG, 1);
		// key 序列化方式
		props.put(ProducerConfig.KEY_SERIALIZER_CLASS_CONFIG, StringSerializer.class.getName());
		// value 序列化方式
		props.put(ProducerConfig.VALUE_SERIALIZER_CLASS_CONFIG, StringSerializer.class.getName());
		return props;
	}

}
