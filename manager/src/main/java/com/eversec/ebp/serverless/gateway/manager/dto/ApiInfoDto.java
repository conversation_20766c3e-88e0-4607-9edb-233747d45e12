package com.eversec.ebp.serverless.gateway.manager.dto;

import com.eversec.ebp.serverless.gateway.dao.dto.ApiPublishStatus;
import com.eversec.ebp.serverless.gateway.dao.entity.ApiInfo;
import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * API资产表
 * </p>
 *
 * <AUTHOR> name
 * @since 2025-05-21
 */
@Getter
@Setter
public class ApiInfoDto implements Serializable {
    private Long id;

    private String name;

    private String interfaceAddress;

    private String proxyPath;

    private Long accessPolicyId;

    private String accessPolicyName;

    private String proxyAddress;

    private ApiPublishStatus publishStatus;

    private LocalDateTime createTime;

    private String createUser;

    private LocalDateTime updateTime;

    private String updateUser;

    public ApiInfoDto() {
    }

    public ApiInfoDto(ApiInfo apiInfo) {
        // apiinfo 全部字段赋值ApiInfoDto
        this.id = apiInfo.getId();
        this.name = apiInfo.getName();
        this.interfaceAddress = apiInfo.getInterfaceAddress();
        this.proxyPath = apiInfo.getProxyPath();
        this.accessPolicyId = apiInfo.getAccessPolicyId();
        this.proxyAddress = apiInfo.getProxyAddress();
        this.publishStatus = apiInfo.getPublishStatus();
        this.createTime = apiInfo.getCreateTime();
        this.createUser = apiInfo.getCreateUser();
        this.updateTime = apiInfo.getUpdateTime();
        this.updateUser = apiInfo.getUpdateUser();
    }
}
