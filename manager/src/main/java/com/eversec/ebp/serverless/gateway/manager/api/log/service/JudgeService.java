package com.eversec.ebp.serverless.gateway.manager.api.log.service;

import com.alibaba.fastjson.JSONException;
import com.alibaba.fastjson.JSONObject;
import com.eversec.ebp.serverless.gateway.manager.api.log.entity.LogLlmResponse;
import com.eversec.ebp.serverless.gateway.manager.log.domain.AccessLog;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

/**
 * 名称: JudgeService<br>
 * 描述: 研判业务，负责调用研判引擎接口并更新数据库<br>
 * 最近处理时间: 2025/4/24 17:37<br>
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class JudgeService {

	@Value("${gateway.log.judge.engine.name}")
	private String name;

	@Autowired
	JudgmentAdapterService judgmentService;

	@Autowired
	AccessLogInfoService accessLogInfoService;

	@Autowired
	LogLlmResponseService logLlmResponseService;

	/**
	 * 研判大模型问答内容风险
	 *
	 * @param batchNo
	 */
	public void judgeByLLM(String batchNo) {
		log.debug("准备开始研判：{}", batchNo);
		final long timeMillis = System.currentTimeMillis();
		AccessLog accessLog = accessLogInfoService.getLogAggregation(batchNo);
		String query = null;
		try {
			JSONObject req = JSONObject.parseObject(accessLog.getRequestBody());
			query = req.getString("query");
		} catch (JSONException e) {
			log.debug("非大模型问答，{}", batchNo);
			//更新这批日志的研判结果为 已研判
			accessLogInfoService.updateJudgeStatus(batchNo);
			return;
		}
		String answer = accessLog.getAllRspData();
		//进行研判和结果保存
		JSONObject jsonObject = null;
		try {
			jsonObject = judgmentService.judgeByDefaultLLM("api-gateway", query, answer);
		} catch (Exception e) {
			log.info("对日志进行研判时出错：{}", batchNo);
		}
		if (jsonObject != null) {
			//入库安全事件
			LogLlmResponse logLlmResponse = new LogLlmResponse();
			logLlmResponse.setBatchNo(batchNo);
			logLlmResponse.setLlmName(name);
			logLlmResponse.setQuery(query);
			logLlmResponse.setAnswer(answer);
			logLlmResponse.setConfidenceLevel1(jsonObject.getJSONObject("置信度").getInteger("用户意图"));
			logLlmResponse.setConfidenceLevel2(jsonObject.getJSONObject("置信度").getInteger("模型回答"));
			logLlmResponse.setCreateTime(System.currentTimeMillis());
			logLlmResponse.setRiskType(jsonObject.getString("恶意类型"));
			logLlmResponse.setRisk(jsonObject.getString("是否安全"));
			logLlmResponse.setAnalysis(jsonObject.getString("分析"));
			logLlmResponse.setAction(jsonObject.getString("建议动作"));
			logLlmResponseService.save(logLlmResponse);
		}
		//更新这批日志的研判结果为 已研判
		accessLogInfoService.updateJudgeStatus(batchNo);
		final long timeMillis2 = System.currentTimeMillis();
		log.info("完成一条日志风险研判：{},耗时：{}ms", batchNo, timeMillis2 - timeMillis);
	}

	public void judgeByDeepSeek(String batchNo) {
		log.debug("准备开始研判：{}", batchNo);
		final long timeMillis = System.currentTimeMillis();
		AccessLog accessLog = accessLogInfoService.getLogAggregation(batchNo);
		String query = null;
		try {
			JSONObject req = JSONObject.parseObject(accessLog.getRequestBody());
			query = req.getString("query");
		} catch (JSONException e) {
			log.debug("非大模型问答，{}", batchNo);
			//更新这批日志的研判结果为 已研判
			accessLogInfoService.updateJudgeStatus(batchNo);
			return;
		}
		String answer = accessLog.getAllRspData();
		//进行研判和结果保存
		JSONObject jsonObject = null;
		try {
			jsonObject = judgmentService.judgeByDeepSeek("api-gateway", query, answer);
		} catch (Exception e) {
			log.info("对日志进行研判时出错：{}", batchNo);
		}
		if (jsonObject != null) {
			//入库安全事件
			LogLlmResponse logLlmResponse = new LogLlmResponse();
			logLlmResponse.setBatchNo(batchNo);
			logLlmResponse.setLlmName(name);
			logLlmResponse.setQuery(query);
			logLlmResponse.setAnswer(answer);
			logLlmResponse.setCreateTime(System.currentTimeMillis());

			// 提取 response 字段
			String responseStr = jsonObject.getString("response");

			// 解析 response 字段的 JSON 字符串
			JSONObject responseJsonObject = JSONObject.parseObject(responseStr);
			// 提取置信度对象
			JSONObject confidenceObj = responseJsonObject.getJSONObject("置信度");
			if (confidenceObj != null) {
				// 这里假设 JSON 中的置信度是小数，先获取为 double 类型再转为 int
				Double userIntentConfidence = confidenceObj.getDoubleValue("用户意图");
				Double modelAnswerConfidence = confidenceObj.getDoubleValue("模型回答");
				logLlmResponse.setConfidenceLevel1(userIntentConfidence != null ? userIntentConfidence.intValue() : null);
				logLlmResponse.setConfidenceLevel2(modelAnswerConfidence != null ? modelAnswerConfidence.intValue() : null);
			}

			logLlmResponse.setCreateTime(System.currentTimeMillis());
			logLlmResponse.setRiskType(responseJsonObject.getString("恶意类型"));
			logLlmResponse.setRisk(responseJsonObject.getString("是否安全"));
			logLlmResponse.setAnalysis(responseJsonObject.getString("分析"));
			logLlmResponse.setAction(responseJsonObject.getString("建议动作"));
			logLlmResponseService.save(logLlmResponse);
		}
		//更新这批日志的研判结果为 已研判
		accessLogInfoService.updateJudgeStatus(batchNo);
		final long timeMillis2 = System.currentTimeMillis();
		log.info("完成一条日志风险研判：{},耗时：{}ms", batchNo, timeMillis2 - timeMillis);
	}

}
