package com.eversec.ebp.serverless.gateway.manager.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.eversec.ebp.serverless.gateway.dao.entity.ApiAccess;
import com.eversec.ebp.serverless.gateway.manager.service.ApiAccessService;
import com.eversec.framework.core.exception.BusinessException;
import com.eversec.framework.core.response2.ResponseHolder;
import com.eversec.framework.core.response2.RestResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 访问策略控制器
 */
@Slf4j
@RestController
@RequestMapping("/api/access")
@Api(tags = "访问策略管理", description = "提供访问策略的增删改查接口")
public class ApiAccessController {

	@Autowired
	private ApiAccessService apiAccessService;

	/**
	 * 查询所有访问策略
	 */
	@GetMapping
	@ApiOperation(value = "查询所有访问策略", notes = "获取所有访问策略列表")
	public RestResponse<List<ApiAccess>> list() {
		List<ApiAccess> list = apiAccessService.list();
		return ResponseHolder.build(() -> list);
	}

	/**
	 * 分页查询访问策略
	 */
	@GetMapping("/page")
	@ApiOperation(value = "分页查询访问策略", notes = "分页获取访问策略列表")
	@ApiImplicitParams({ @ApiImplicitParam(name = "current", value = "当前页码", required = true, dataType = "long", paramType = "query"),
			@ApiImplicitParam(name = "size", value = "每页记录数", required = true, dataType = "long", paramType = "query"),
			@ApiImplicitParam(name = "name", value = "策略名称", required = false, dataType = "String", paramType = "query") })
	public RestResponse<IPage<ApiAccess>> page(@RequestParam(value = "current", defaultValue = "1") long current,
			@RequestParam(value = "size", defaultValue = "10") long size, @RequestParam(value = "name", required = false) String name) {

		Page<ApiAccess> page = new Page<>(current, size);
		LambdaQueryWrapper<ApiAccess> queryWrapper = new LambdaQueryWrapper<>();

		if (name != null && !name.isEmpty()) {
			queryWrapper.like(ApiAccess::getName, name);
		}

		queryWrapper.orderByDesc(ApiAccess::getCreate_time);

		IPage<ApiAccess> result = apiAccessService.page(page, queryWrapper);
		return ResponseHolder.build(() -> result);
	}

	/**
	 * 根据ID查询访问策略
	 */
	@GetMapping("/{id}")
	@ApiOperation(value = "根据ID查询访问策略", notes = "获取指定ID的访问策略详情")
	@ApiImplicitParam(name = "id", value = "访问策略ID", required = true, dataType = "String", paramType = "path")
	public RestResponse<ApiAccess> getById(@PathVariable String id) {
		ApiAccess apiAccess = apiAccessService.getById(id);
		if (apiAccess != null) {
			return ResponseHolder.build(() -> apiAccess);
		} else {
			throw new BusinessException(404, "访问策略不存在");
		}
	}

	/**
	 * 创建访问策略
	 */
	@PostMapping
	@ApiOperation(value = "创建访问策略", notes = "创建新的访问策略")
	public RestResponse<ApiAccess> create(@RequestBody ApiAccess apiAccess) {

		long currentTime = System.currentTimeMillis();
		apiAccess.setCreate_time(currentTime);
		apiAccess.setUpdate_time(currentTime);

		boolean success = apiAccessService.save(apiAccess);
		if (success) {
			return ResponseHolder.build(() -> apiAccess);
		} else {
			throw new BusinessException(-1, "创建失败");
		}
	}

	/**
	 * 更新访问策略
	 */
	@PutMapping("/{id}")
	@ApiOperation(value = "更新访问策略", notes = "更新指定ID的访问策略")
	@ApiImplicitParam(name = "id", value = "访问策略ID", required = true, dataType = "String", paramType = "path")
	public RestResponse<ApiAccess> update(@PathVariable Integer id, @RequestBody ApiAccess apiAccess) {
		if (apiAccessService.getById(id) == null) {
			throw new BusinessException(404, "访问策略不存在");
		}

		apiAccess.setId(id);
		apiAccess.setUpdate_time(System.currentTimeMillis());

		boolean success = apiAccessService.updateById(apiAccess);
		if (success) {
			return ResponseHolder.build(() -> apiAccess);
		} else {
			throw new BusinessException(-1, "更新失败");
		}
	}

	/**
	 * 删除访问策略
	 */
	@DeleteMapping("/{id}")
	@ApiOperation(value = "删除访问策略", notes = "删除指定ID的访问策略")
	@ApiImplicitParam(name = "id", value = "访问策略ID", required = true, dataType = "String", paramType = "path")
	public RestResponse<Boolean> delete(@PathVariable String id) {
		boolean success = apiAccessService.removeById(id);
		if (success) {
			return ResponseHolder.build(() -> true);
		} else {
			throw new BusinessException(-1, "删除失败");
		}
	}
}