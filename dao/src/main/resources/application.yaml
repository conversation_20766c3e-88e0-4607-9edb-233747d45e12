mybatis-plus:
  type-aliases-package: com.example.common.entity  # 实体类包路径
  configuration:
    map-underscore-to-camel-case: true  # 开启驼峰命名转换
    cache-enabled: false  # 禁用二级缓存
    log-impl: org.apache.ibatis.logging.slf4j.Slf4jImpl  # 使用SLF4J记录SQL日志
  global-config:
    db-config:
      id-type: auto  # 主键生成策略，可选AUTO、ASSIGN_ID等
      logic-delete-field: deleted  # 逻辑删除字段名
      logic-not-delete-value: 0  # 未删除值
      logic-delete-value: 1  # 已删除值
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: ******************************************************************************************************************************
    username: root
    password: H*y%eM17Qtz64ZafMysql
    type: com.zaxxer.hikari.HikariDataSource  # 使用HikariCP连接池
    hikari:
      minimum-idle: 5
      maximum-pool-size: 15
      auto-commit: true
      idle-timeout: 30000
      pool-name: HikariCP
      max-lifetime: 1800000
      connection-timeout: 30000
      connection-test-query: SELECT 1