package com.eversec.ebp.serverless.gateway.manager.service;


import com.baomidou.mybatisplus.extension.service.IService;
import com.eversec.ebp.serverless.gateway.dao.entity.ApiInfo;
import com.eversec.ebp.serverless.gateway.manager.dto.ApiInfoQueryDto;
import com.eversec.framework.core.data.QueryResult;

/**
 * <p>
 * API资产表 服务类
 * </p>
 *
 * <AUTHOR> name
 * @since 2025-05-21
 */
public interface ApiInfoService extends IService<ApiInfo> {

    QueryResult<ApiInfo> getPage(ApiInfoQueryDto apiInfoQueryDto);

    Integer add(ApiInfo apiInfo);

    Integer publish(String id);

    Integer unpublish(String id);

    Integer addAccessPolicy(String id, String accessPolicyId);
}
