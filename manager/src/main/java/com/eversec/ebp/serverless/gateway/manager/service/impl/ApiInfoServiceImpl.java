package com.eversec.ebp.serverless.gateway.manager.service.impl;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.eversec.ebp.serverless.gateway.dao.dto.ApiPublishStatus;
import com.eversec.ebp.serverless.gateway.dao.entity.ApiAccess;
import com.eversec.ebp.serverless.gateway.dao.entity.ApiInfo;
import com.eversec.ebp.serverless.gateway.dao.mapper.ApiAccessMapper;
import com.eversec.ebp.serverless.gateway.dao.mapper.ApiInfoMapper;
import com.eversec.ebp.serverless.gateway.manager.dto.*;
import com.eversec.ebp.serverless.gateway.manager.service.ApiAccessRestrictService;
import com.eversec.ebp.serverless.gateway.manager.service.ApiInfoService;
import com.eversec.framework.core.data.QueryResult;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * API资产表 服务实现类
 * </p>
 *
 * <AUTHOR> name
 * @since 2025-05-21
 */
@Service
@Transactional(readOnly = true)
public class ApiInfoServiceImpl extends ServiceImpl<ApiInfoMapper, ApiInfo> implements ApiInfoService {


    @Autowired
    private ApiInfoMapper apiInfoMapper;
    @Autowired
    private ApiAccessMapper apiAccessMapper;
    @Autowired
    private ApiAccessRestrictService apiAccessRestrictService;

    /**
     * 分页查询应用
     *
     * @param apiInfoQueryDto 查询条件
     * @return
     */
    public QueryResult<ApiInfoDto> getPage(ApiInfoQueryDto apiInfoQueryDto) {
        IPage<ApiInfo> infoPageList = getPageByQuery(apiInfoQueryDto);
        //获取查询数据
        List<ApiInfoDto> apiInfoList = infoPageList.getRecords().stream().map(ApiInfoDto::new)
                .peek(apiInfoDto -> {
                    // 赋值策略名称
                    ApiAccess apiAccess = apiAccessMapper.selectById(apiInfoDto.getAccessPolicyId());
                    apiInfoDto.setAccessPolicyName(Optional.ofNullable(apiAccess).map(ApiAccess::getName).orElse(""));
                }).collect(Collectors.toList());
        QueryResult<ApiInfoDto> result = new QueryResult<>();
        result.setResultData(apiInfoList);
        result.setTotalRecord(infoPageList.getTotal());
        return result;
    }

    private Wrapper<ApiInfo> getAppInfoQueryWrapper(ApiInfoQueryDto condition) {
        QueryWrapper<ApiInfo> appWrapper = new QueryWrapper<>();

        if (StringUtils.isNotBlank(condition.getName())) {
            appWrapper.like("name", condition.getName());
        }

        appWrapper.orderByDesc("create_time");
        return appWrapper;
    }


    /**
     * 分页查询应用
     *
     * @param apiInfoQueryDto 查询条件
     * @return 每页的资产数据
     */
    public IPage<ApiInfo> getPageByQuery(ApiInfoQueryDto apiInfoQueryDto) {
        //设置分页信息 传递参数 页数  每页条数
        Page<ApiInfo> page = new Page<>(apiInfoQueryDto.getPageNum(), apiInfoQueryDto.getPageSize());
        //查询条件
        Wrapper<ApiInfo> queryWrapper = getAppInfoQueryWrapper(apiInfoQueryDto);
        return apiInfoMapper.selectPage(page, queryWrapper);
    }

    @Override
    public QueryResult<ApiInfoExtraDto> getPageExtra(ApiInfoQueryDto apiInfoQueryDto) {
        IPage<ApiInfo> apiInfoIPage = getPageByQuery(apiInfoQueryDto);
        QueryResult<ApiInfoExtraDto> result = new QueryResult<>();
        List<ApiInfo> apiInfoList = new ArrayList<>(apiInfoIPage.getRecords());
        // 获取策略信息，资产和策略一对一
        List<Long> apiAccessIds = apiInfoList.stream().map(ApiInfo::getAccessPolicyId).collect(Collectors.toList());
        Map<Long, ApiAccessDto> accessDtoMap = apiAccessMapper.selectBatchIds(apiAccessIds).stream()
                .collect(Collectors.toMap(ApiAccess::getId, ApiAccessDto::new));
        // 获取封堵策略列表
        List<Long> apiInfoIds = apiInfoList.stream().map(ApiInfo::getId).collect(Collectors.toList());
        // 转成策略<id、封堵列表>的形式
        Map<Long, List<ApiAccessRestrictDto>> accessRestrictDtoMap = new HashMap<>();
        apiAccessRestrictService.getApiAccessRestrictByApiInfoIds(apiInfoIds).forEach(
                apiAccessRestrict -> {
                    List<ApiAccessRestrictDto> dtos = accessRestrictDtoMap.getOrDefault(apiAccessRestrict.getAssetId(), new ArrayList<>());
                    dtos.add(new ApiAccessRestrictDto(apiAccessRestrict));
                    accessRestrictDtoMap.put(apiAccessRestrict.getAssetId(), dtos);
                });

        result.setResultData(apiInfoIPage.getRecords().stream().map(apiInfo -> {
            ApiInfoExtraDto apiInfoExtraDto = new ApiInfoExtraDto(apiInfo);
            apiInfoExtraDto.setApiAccess(accessDtoMap.get(apiInfo.getAccessPolicyId()));
            apiInfoExtraDto.setApiAccessRestrictList(accessRestrictDtoMap.getOrDefault(apiInfo.getId(), new ArrayList<>()));
            return apiInfoExtraDto;
        }).collect(Collectors.toList()));

        result.setTotalRecord(apiInfoIPage.getTotal());
        return result;
    }

    /**
     * 新增资产
     *
     * @param apiInfo 资产信息
     * @return
     */
    @Transactional(readOnly = false, propagation = Propagation.REQUIRED)
    public Integer add(ApiInfo apiInfo) {
        // TODO 生成proxy_address
        return apiInfoMapper.insert(apiInfo);

    }

    /**
     * 发布资产
     *
     * @param id 资产id
     * @return
     */
    @Transactional(readOnly = false, propagation = Propagation.REQUIRED)
    public Integer publish(Long id) {
        ApiInfo apiInfo = apiInfoMapper.selectById(id);
        if (apiInfo.getId() == null) {
            return -1;
        }
        apiInfo.setPublishStatus(ApiPublishStatus.ONLINE);
        return apiInfoMapper.updateById(apiInfo);
    }

    /**
     * 发布资产
     *
     * @param id 资产id
     * @return
     */
    @Transactional(readOnly = false, propagation = Propagation.REQUIRED)
    public Integer unpublish(Long id) {
        ApiInfo apiInfo = apiInfoMapper.selectById(id);
        if (apiInfo.getId() == null) {
            return -1;
        }
        apiInfo.setPublishStatus(ApiPublishStatus.OFFLINE);
        return apiInfoMapper.updateById(apiInfo);
    }


    /**
     * 添加资产访问策略
     *
     * @param id             资产id
     * @param accessPolicyId 资产访问策略id
     * @return
     */
    @Override
    @Transactional(readOnly = false, propagation = Propagation.REQUIRED)
    public Integer addAccessPolicy(Long id, Long accessPolicyId) {
        ApiInfo apiInfo = apiInfoMapper.selectById(id);
        if (apiInfo == null || apiInfo.getId() == null) {
            return -1;
        }
        apiInfo.setAccessPolicyId(accessPolicyId);
        return apiInfoMapper.updateById(apiInfo);
    }

    /**
     * 根据访问策略ID统计防护资产数量
     *
     * @param accessPolicyId 访问策略ID
     * @return 防护资产数量
     */
    @Override
    public Integer countByAccessPolicyId(Long accessPolicyId) {
        if (accessPolicyId > 0) {
            return 0;
        }
        QueryWrapper<ApiInfo> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("access_policy_id", accessPolicyId);
        queryWrapper.eq("deleted", false); // 排除已删除的记录
        return Math.toIntExact(apiInfoMapper.selectCount(queryWrapper));
    }
}
