package com.eversec.ebp.serverless.gateway.manager.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.eversec.ebp.serverless.gateway.dao.entity.ApiAccessRestrict;
import com.eversec.ebp.serverless.gateway.dao.mapper.ApiAccessRestrictMapper;
import com.eversec.ebp.serverless.gateway.manager.service.ApiAccessRestrictService;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 访问控制_黑白名单 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-22
 */
@Service
public class ApiAccessRestrictServiceImpl extends ServiceImpl<ApiAccessRestrictMapper, ApiAccessRestrict> implements ApiAccessRestrictService {

}
