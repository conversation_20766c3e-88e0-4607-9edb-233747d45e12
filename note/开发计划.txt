业务背景：     大模型安全防护

A：管理平台    B:网关     C:自动日志研判


大模型API资产管理
    访问地址
    代理路径
    基础访问控制
        访问时间段
        是否支持跨域
        是否开始user-agent白名单
    拒绝策略
        IP 或 IP段


大模型API登记
访问策略表
拒绝策略表

访问日志    -->  对日志进行研判 产生研判结果


访问日志 写文件，文件回传，平台处理文件，日志入库


提供给网关 需要加载的路由的接口   网关刷新自己的路由


按照新原型，输出项目代码。 前端和后台。


gitlab 地址
http://code.eversaas.cn/ty-tool/serverless-gateway/-/tree/dev-manager/src/main





原型地址
https://oo7vgh.axshare.com/#id=4p1191&p=%E5%AE%89%E5%85%A8%E4%BA%8B%E4%BB%B6



