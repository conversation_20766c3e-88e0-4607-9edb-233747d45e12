server:
  port: 8080

spring:
  application:
    name: gateway-manager-wjl
  logging:
    file:
      path: ${user.dir}/logs
      name: ${user.dir}/logs/${spring.application.name}.log
    level:
      root: DEBUG
      com.eversec: INFO
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8
  swagger:
    package: com.eversec.ebp.serverless
  common:
    web:
      swagger:
        enable: true
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: jdbc:mysql://**********:31269/api_gateway?serverTimezone=Asia/Shanghai&characterEncoding=utf-8&autoReconnect=true&useSSL=false
    username: root
    password: netDc@#su
    hikari:
      minimum-idle: 5
      maximum-pool-size: 15
      auto-commit: true
      idle-timeout: 30000
      pool-name: HikariCP
      max-lifetime: 1800000
      connection-timeout: 30000
      connection-test-query: SELECT 1

eureka:
  instance:
    preferIpAddress: false
    leaseRenewalIntervalInSeconds: 10
    leaseExpirationDurationInSeconds: 20
  client:
    registerWithEureka: true
    fetchRegistry: true
    serviceUrl:
      defaultZone: http://cloud.everoffice.cn:30617/eureka/
  healthcheck:
    enabled: true

mybatis-plus:
  mapper-locations: classpath:mapper/*.xml
  type-aliases-package: com.eversec.ebp.serverless.gateway.manager
  type-enums-package: com.eversec.ebp.serverless.gateway.dao
  configuration:
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl


# 网关自定义配置
gateway:
  #认证（type: none/signature/basic/factory，class: metadata/ebp/api）
  auth:
    class: api
    type: factory
    signature:
      period: 5

  #路由存储的文件位置（type: redis/file/mysql）
  route:
    repository:
      type: mysql
      file: ${user.dir}/data/route.txt

  #输出日志
  log:
    output:
      buffer:
        cron: 0 */1 * * * ?
        size: 128
      kafka:
        enable: false
        bootstrap-servers: 192.168.205.232:29092
        topic: ACCESS_LOG
      file:
        enable: false
        dir: ${user.dir}/data/access
      mysql:
        enable: true
        delete:
          brfore: 3
      sample:
        enable: false
        dir: ${user.dir}/data/sample
    judge:
      enable: true
      engine:
        url: http://10.0.14.51/v1/chat-messages
        auth: 'Bearer app-GAPXnsmofTj34tLJlbx5Zl80'
        name: 'MAP-大模型攻击检测智能体'
  rule:
    path: ${user.dir}/data/rule.db
  domain:
    url: apigateway.eversec.com
  license:
    filePath: ${user.dir}/data/license/license.lic
    productPath: ${user.dir}/data/license/product.info
    enable: true
  sso:
    time: 30
    exrire_time: 1800000
  #路由上下线通知
  route-notice:
    kafka:
      enable: false
      bootstrap-servers: 192.168.205.233:9092
      topic_offline: route_offline
      topic_online: route_online
