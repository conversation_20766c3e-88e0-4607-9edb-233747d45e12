server:
  port: 48080

spring:
  application:
    name: gateway-manager
  logging:
    file:
      path: ${user.dir}/logs
      name: ${user.dir}/logs/${spring.application.name}.log
    level:
      root: DEBUG
      com.eversec: INFO
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8
  swagger:
    package: com.eversec.ebp.serverless
  common:
    web:
      swagger:
        enable: true
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: ***********************************************************************************************************************************
    username: root
    password: netDc@#su
    hikari:
      minimum-idle: 5
      maximum-pool-size: 15
      auto-commit: true
      idle-timeout: 30000
      pool-name: HikariCP
      max-lifetime: 1800000
      connection-timeout: 30000
      connection-test-query: SELECT 1

eureka:
  instance:
    preferIpAddress: true
    leaseRenewalIntervalInSeconds: 10
    leaseExpirationDurationInSeconds: 20
    ip-address: *************
  client:
    registerWithEureka: true
    fetchRegistry: true
    serviceUrl:
      defaultZone: http://cloud.everoffice.cn:30617/eureka/
  healthcheck:
    enabled: true