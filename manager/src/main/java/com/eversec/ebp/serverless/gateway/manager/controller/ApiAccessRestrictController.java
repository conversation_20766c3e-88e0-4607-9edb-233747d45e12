package com.eversec.ebp.serverless.gateway.manager.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.eversec.ebp.serverless.gateway.dao.entity.ApiAccessRestrict;
import com.eversec.ebp.serverless.gateway.manager.service.ApiAccessRestrictService;
import com.eversec.framework.core.exception.BusinessException;
import com.eversec.framework.core.response2.ResponseHolder;
import com.eversec.framework.core.response2.RestResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 封堵策略控制器
 */
@Slf4j
@RestController
@RequestMapping("/api/access/restrict")
@Api(tags = "封堵策略管理", description = "提供封堵策略的增删改查接口")
public class ApiAccessRestrictController {

	@Autowired
	private ApiAccessRestrictService apiAccessRestrictService;

	/**
	 * 查询所有封堵策略
	 */
	@GetMapping
	@ApiOperation(value = "查询所有封堵策略", notes = "获取所有封堵策略列表")
	public RestResponse<List<ApiAccessRestrict>> list() {
		List<ApiAccessRestrict> list = apiAccessRestrictService.list();
		return ResponseHolder.build(() -> list);
	}

	/**
	 * 分页查询封堵策略
	 */
	@GetMapping("/page")
	@ApiOperation(value = "分页查询封堵策略", notes = "分页获取封堵策略列表")
	@ApiImplicitParams({ @ApiImplicitParam(name = "current", value = "当前页码", required = true, dataType = "long", paramType = "query"),
			@ApiImplicitParam(name = "size", value = "每页记录数", required = true, dataType = "long", paramType = "query"),
			@ApiImplicitParam(name = "action", value = "动作（允许/拒绝）", required = false, dataType = "String", paramType = "query"),
			@ApiImplicitParam(name = "restrictionType", value = "限制类型", required = false, dataType = "Integer", paramType = "query") })
	public RestResponse<IPage<ApiAccessRestrict>> page(@RequestParam(value = "current", defaultValue = "1") long current,
			@RequestParam(value = "size", defaultValue = "10") long size, @RequestParam(value = "action", required = false) String action,
			@RequestParam(value = "restrictionType", required = false) Integer restrictionType) {

		Page<ApiAccessRestrict> page = new Page<>(current, size);
		LambdaQueryWrapper<ApiAccessRestrict> queryWrapper = new LambdaQueryWrapper<>();

		// 添加查询条件
		if (action != null && !action.isEmpty()) {
			queryWrapper.eq(ApiAccessRestrict::getAction, action);
		}

		if (restrictionType != null) {
			queryWrapper.eq(ApiAccessRestrict::getRestriction_type, restrictionType);
		}

		IPage<ApiAccessRestrict> result = apiAccessRestrictService.page(page, queryWrapper);
		return ResponseHolder.build(() -> result);
	}

	/**
	 * 根据ID查询封堵策略
	 */
	@GetMapping("/{id}")
	@ApiOperation(value = "根据ID查询封堵策略", notes = "获取指定ID的封堵策略详情")
	@ApiImplicitParam(name = "id", value = "封堵策略ID", required = true, dataType = "String", paramType = "path")
	public RestResponse<ApiAccessRestrict> getById(@PathVariable String id) {
		ApiAccessRestrict apiAccessRestrict = apiAccessRestrictService.getById(id);
		if (apiAccessRestrict != null) {
			return ResponseHolder.build(() -> apiAccessRestrict);
		} else {
			throw new BusinessException(404, "封堵策略不存在");
		}
	}

	/**
	 * 创建封堵策略
	 */
	@PostMapping
	@ApiOperation(value = "创建封堵策略", notes = "创建新的封堵策略")
	public RestResponse<ApiAccessRestrict> create(@RequestBody ApiAccessRestrict apiAccessRestrict) {

		boolean success = apiAccessRestrictService.save(apiAccessRestrict);
		if (success) {
			return ResponseHolder.build(() -> apiAccessRestrict);
		} else {
			throw new BusinessException(-1, "创建失败");
		}
	}

	/**
	 * 更新封堵策略
	 */
	@PutMapping("/{id}")
	@ApiOperation(value = "更新封堵策略", notes = "更新指定ID的封堵策略")
	@ApiImplicitParam(name = "id", value = "封堵策略ID", required = true, dataType = "String", paramType = "path")
	public RestResponse<Boolean> update(@PathVariable Integer id, @RequestBody ApiAccessRestrict apiAccessRestrict) {
		// 检查是否存在
		if (apiAccessRestrictService.getById(id) == null) {
			throw new BusinessException(404, "封堵策略不存在");
		}
		// 设置ID
		apiAccessRestrict.setId(id);

		boolean success = apiAccessRestrictService.updateById(apiAccessRestrict);
		if (success) {
			return ResponseHolder.build(() -> true);
		} else {
			throw new BusinessException(-1, "更新失败");
		}
	}

	/**
	 * 删除封堵策略
	 */
	@DeleteMapping("/{id}")
	@ApiOperation(value = "删除封堵策略", notes = "删除指定ID的封堵策略")
	@ApiImplicitParam(name = "id", value = "封堵策略ID", required = true, dataType = "String", paramType = "path")
	public RestResponse<Boolean> delete(@PathVariable String id) {
		boolean success = apiAccessRestrictService.removeById(id);
		if (success) {
			return ResponseHolder.build(() -> true);
		} else {
			throw new BusinessException(-1, "删除封堵策略失败");
		}
	}
}
