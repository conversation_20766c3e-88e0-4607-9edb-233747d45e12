package com.eversec.ebp.serverless.gateway.manager.api.log.dto;

import com.eversec.ebp.serverless.gateway.manager.common.PageParams;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-31
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class LogLlmResponseQueryDto extends PageParams {

	@ApiModelProperty(value = "日志批次号")
	private String batchNo;

}
