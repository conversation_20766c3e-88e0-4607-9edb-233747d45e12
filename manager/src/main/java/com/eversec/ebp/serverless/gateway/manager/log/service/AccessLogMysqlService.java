package com.eversec.ebp.serverless.gateway.manager.log.service;

import com.eversec.ebp.serverless.gateway.manager.log.domain.AccessLog;
import com.eversec.ebp.serverless.gateway.manager.log.service.mysql.IAccessLogService;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnExpression;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * 类名称: AccessLogMysqlService<br>
 * 类描述: 日志通过输出的通道<br>
 * 修改时间: 2022年1月12日<br>
 *
 * <AUTHOR>
 */
@Service("AccessLogMysqlService")
@ConditionalOnExpression("${gateway.log.output.mysql.enable:false}==true")
public class AccessLogMysqlService implements AccessLogService {

    private static Logger logger = LoggerFactory.getLogger(AccessLogMysqlService.class);

    @Value("${gateway.log.output.mysql.delete.before:3}")
    private int day;

    @Autowired
    private IAccessLogService accessLogService;

    @Override
    synchronized public void output(List<AccessLog> accessLogs) {
        Set<String> batchNos = new HashSet<>();
        for (AccessLog accessLog: accessLogs) {
            batchNos.add(accessLog.getBatchNo());
        }
        logger.debug("日志输出到mysql，消息批次号：{}", StringUtils.join(batchNos, ","));
        accessLogService.saveBatch(accessLogs);
    }

    @Scheduled(cron = "${gateway.log.output.file.buffer.cron:0 */5 * * * ?}")
    public void clearHis() {
        //TODO 马腾，2025-3-27因调整accesslog入单表，暂时注释掉，后续需要在这里编写定时删除accesslog的数据
//        logger.debug("定时删除过期历史日志执行开始" + new Date());
//        final long l = System.currentTimeMillis();
//        //删除数据的天数day
//        Long time = l - day * 86400000L;
//        final QueryWrapper<AccessLogMain> accessLogMainQueryWrapper = new QueryWrapper<AccessLogMain>();
//        accessLogMainQueryWrapper.lt("request_time", time);
//        iAccessLogMainService.remove(accessLogMainQueryWrapper);
//        iAccessLogDetailService.removeDetail();
//        logger.debug("定时删除过期历史日志执行完毕");
//        output();
    }
}
