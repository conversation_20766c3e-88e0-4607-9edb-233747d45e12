package com.eversec.ebp.serverless.gateway.manager.api.log.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.eversec.ebp.serverless.gateway.manager.api.log.dto.AccessLogQueryDto;
import com.eversec.ebp.serverless.gateway.manager.log.domain.AccessLog;
import com.eversec.framework.core.data.QueryResult;

import java.util.List;

public interface AccessLogInfoService extends IService<AccessLog> {

	//分页查询日志-不进行批次号聚合
	 QueryResult<AccessLog> getApiAccessLogPageList(AccessLogQueryDto query);

	 //分页查询日志-按批次号聚合（避免同一批次号重复展示）
	 QueryResult<AccessLog> getApiAccessLogPageListGroupByBatch(AccessLogQueryDto query);

	 AccessLog getLogAggregation(String batchNo);

	 String getOneToJudge();

	 void updateJudgeStatus(String batchNo);

	 List<String> getIdListByBatchNo(String batchNo);

	AccessLog getOneByAccessLogId(String logId);
}
