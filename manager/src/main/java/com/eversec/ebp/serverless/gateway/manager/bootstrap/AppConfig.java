package com.eversec.ebp.serverless.gateway.manager.bootstrap;

import com.eversec.framework.springboot.BaseAppConfig;
import org.springframework.boot.autoconfigure.http.HttpMessageConverters;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.converter.json.MappingJackson2HttpMessageConverter;
import springfox.documentation.builders.ApiInfoBuilder;
import springfox.documentation.service.ApiInfo;

@Configuration
public class AppConfig extends BaseAppConfig {

    @Bean
    public HttpMessageConverters httpMessageConverters(MappingJackson2HttpMessageConverter jsonConverter) {
        return new HttpMessageConverters(jsonConverter);
    }


    @Override
    protected ApiInfo apiInfo() {
        return new ApiInfoBuilder()
                .title("Serverless Gateway manager 构建RESTful APIs")
                .description("Swagger3构建RESTful APIs")
                .version("1.1.1")
                .build();
    }
}
