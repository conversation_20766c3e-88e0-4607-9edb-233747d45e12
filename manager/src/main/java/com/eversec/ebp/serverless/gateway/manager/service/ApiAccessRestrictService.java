package com.eversec.ebp.serverless.gateway.manager.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.eversec.ebp.serverless.gateway.dao.entity.ApiAccessRestrict;
import com.eversec.ebp.serverless.gateway.manager.dto.ApiAccessRestrictQueryDto;
import com.eversec.framework.core.data.QueryResult;

import java.util.Collection;
import java.util.List;

/**
 * <p>
 * 访问控制_黑白名单 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-22
 */
public interface ApiAccessRestrictService extends IService<ApiAccessRestrict> {

    /**
     * 分页查询封堵策略
     *
     * @param apiAccessRestrictQueryDto 查询条件
     * @return 分页结果
     */
    QueryResult<ApiAccessRestrict> getPage(ApiAccessRestrictQueryDto apiAccessRestrictQueryDto);

    /**
     * 新增封堵策略
     *
     * @param apiAccessRestrict 封堵策略信息
     * @return 影响行数
     */
    Integer add(ApiAccessRestrict apiAccessRestrict);

    /**
     * 根据资产id列表获取封堵策略
     *
     * @param apiInfoIds 资产id列表
     * @return 资产列表
     */
    List<ApiAccessRestrict> getApiAccessRestrictByApiInfoIds(Collection<Long> apiInfoIds);
}
