package com.eversec.ebp.serverless.gateway.dao.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 访问日志表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-22
 */
@Getter
@Setter
@TableName("log_access")
public class LogAccess implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 日志的流水_id，用于做信息核对
     */
    @TableField("id")
    private String id;

    /**
     * 日志批次号
     */
    @TableField("batch_no")
    private String batchNo;

    /**
     * 日志类型
     */
    @TableField("log_type")
    private String logType;

    /**
     * 日志命中的规则_id（阻断日志和访问日志没有规则_id）
     */
    @TableField("rule_id")
    private String ruleId;

    /**
     * 路由_id
     */
    @TableField("route_id")
    private String routeId;

    /**
     * 请求ip
     */
    @TableField("request_ip")
    private String requestIp;

    /**
     * 请求地址
     */
    @TableField("request_url")
    private String requestUrl;

    /**
     * 请求时间
     */
    @TableField("request_time")
    private Long requestTime;

    /**
     * 执行时间
     */
    @TableField("execute_time")
    private Long executeTime;

    /**
     * 响应时间
     */
    @TableField("response_time")
    private Long responseTime;

    /**
     * 响应状态码
     */
    @TableField("response_status")
    private Integer responseStatus;

    /**
     * 协议
     */
    @TableField("protocol")
    private String protocol;

    /**
     * 请求路径
     */
    @TableField("request_path")
    private String requestPath;

    /**
     * 请求方法
     */
    @TableField("request_method")
    private String requestMethod;

    /**
     * 请求类型
     */
    @TableField("request_media_type")
    private String requestMediaType;

    /**
     * 请求头
     */
    @TableField("request_header")
    private String requestHeader;

    /**
     * 请求体的大小
     */
    @TableField("request_content_length")
    private Long requestContentLength;

    /**
     * 响应类型
     */
    @TableField("response_media_type")
    private String responseMediaType;

    /**
     * 响应头
     */
    @TableField("response_header")
    private String responseHeader;

    /**
     * 响应体的大小
     */
    @TableField("response_content_length")
    private Long responseContentLength;

    /**
     * 日志标签
     */
    @TableField("tags")
    private String tags;

    /**
     * 请求查询参数
     */
    @TableField("request_query_params")
    private String requestQueryParams;

    /**
     * 请求体
     */
    @TableField("request_body")
    private String requestBody;

    /**
     * 访问实例
     */
    @TableField("target_server")
    private String targetServer;

    /**
     * 响应体
     */
    @TableField("response_data")
    private String responseData;

    /**
     * 路由转发地址
     */
    @TableField("route_uri")
    private String routeUri;

    /**
     * 路由完整信息
     */
    @TableField("route")
    private String route;

    /**
     * 规则大类
     */
    @TableField("main_type")
    private String mainType;

    /**
     * 规则小类
     */
    @TableField("sub_type")
    private String subType;

    /**
     * 0:初始状态：未研判 1：已研判
     */
    @TableField("judgment_status")
    private Byte judgmentStatus;
}
