package com.eversec.ebp.serverless.gateway.manager.service;


import com.baomidou.mybatisplus.extension.service.IService;
import com.eversec.ebp.serverless.gateway.dao.entity.ApiInfo;
import com.eversec.ebp.serverless.gateway.manager.dto.ApiInfoDto;
import com.eversec.ebp.serverless.gateway.manager.dto.ApiInfoExtraDto;
import com.eversec.ebp.serverless.gateway.manager.dto.ApiInfoQueryDto;
import com.eversec.framework.core.data.QueryResult;

/**
 * <p>
 * API资产表 服务类
 * </p>
 *
 * <AUTHOR> name
 * @since 2025-05-21
 */
public interface ApiInfoService extends IService<ApiInfo> {

    QueryResult<ApiInfoDto> getPage(ApiInfoQueryDto apiInfoQueryDto);

    QueryResult<ApiInfoExtraDto> getPageExtra(ApiInfoQueryDto apiInfoQueryDto);

    Integer add(ApiInfo apiInfo);

    Integer publish(Long id);

    Integer unpublish(Long id);

    Integer addAccessPolicy(Long id, Long accessPolicyId);

    /**
     * 根据访问策略ID统计防护资产数量
     *
     * @param accessPolicyId 访问策略ID
     * @return 防护资产数量
     */
    Integer countByAccessPolicyId(Long accessPolicyId);
}
