package com.eversec.ebp.serverless.gateway.manager;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.scheduling.annotation.EnableScheduling;
import springfox.documentation.swagger2.annotations.EnableSwagger2;

@EnableScheduling
@EnableSwagger2
@EnableDiscoveryClient
@ComponentScan(basePackages = {"com.eversec.ebp.serverless.gateway.manager", "com.eversec.ebp.serverless.gateway.dao"})
@MapperScan("com.eversec.ebp.serverless.**.mapper")
@SpringBootApplication
public class ManagerApp {

	private static ApplicationContext context;

	public static void main(String[] args) {
		context = SpringApplication.run(ManagerApp.class, args);
	}

	public static ApplicationContext context() {
		return context;
	}

}




