package com.eversec.ebp.serverless.gateway.manager.log.service;

import com.alibaba.fastjson.JSONObject;
import com.eversec.ebp.serverless.gateway.manager.log.domain.AccessLog;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang.time.DateFormatUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnExpression;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 类名称: AccessLogFileService<br>
 * 类描述: 日志通过文件输出的通道<br>
 * 修改时间: 2021年9月20日<br>
 *
 * <AUTHOR>
 */
@Service("AccessLogFileService")
@ConditionalOnExpression("${gateway.log.output.file.enable:false}==true")
public class AccessLogFileService implements AccessLogService {

    private static Logger logger = LoggerFactory.getLogger(AccessLogFileService.class);

    @Value("${gateway.log.output.file.dir:${user.dir}/logs/access}")
    private String dir;

    @Override
    synchronized public void output(List<AccessLog> accessLogs) {
        List<String> logBuffer = new ArrayList<>();
        for (AccessLog accessLog: accessLogs) {
            String value = JSONObject.toJSONString(accessLog);
            logBuffer.add(value);
        }
        writeLines(logBuffer);
    }

    synchronized private void writeLines(List<String> logBuffer) {
        if (logBuffer.size() == 0) {
            return;
        }
        File path = new File(dir);
        if (!path.exists()) {
            logger.debug("日志目录不存在，生成目录：{}", dir);
            path.mkdirs();
        }
        String mm = DateFormatUtils.format(new Date(), "yyyyMMddHHmm");
        String file = dir + File.separator + mm + ".txt";
        File out = new File(file);
        try {
            if (!out.exists()) {
                out.createNewFile();
            }
            FileUtils.writeLines(out, "UTF-8", logBuffer, true);
        } catch (IOException e) {
            e.printStackTrace();
        }
        logger.debug("{}条日志输出到文件，文件：{}", logBuffer.size(), file);
    }

    @Scheduled(cron = "${gateway.log.output.file.buffer.cron:0 */5 * * * ?}")
    public void timingOutput() {
//        logger.debug("定時輸出日志，緩存大小:{}", logBuffer.size());
//		String mm = DateFormatUtils.format(new Date(), "yyyyMMddHHmm");
//		logBuffer.add(mm);
//        output();
    }
}
