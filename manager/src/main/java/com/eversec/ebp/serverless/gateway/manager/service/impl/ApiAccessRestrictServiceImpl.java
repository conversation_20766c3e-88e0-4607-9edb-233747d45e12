package com.eversec.ebp.serverless.gateway.manager.service.impl;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.eversec.ebp.serverless.gateway.dao.entity.ApiAccessRestrict;
import com.eversec.ebp.serverless.gateway.dao.mapper.ApiAccessRestrictMapper;
import com.eversec.ebp.serverless.gateway.manager.dto.ApiAccessRestrictQueryDto;
import com.eversec.ebp.serverless.gateway.manager.service.ApiAccessRestrictService;
import com.eversec.framework.core.data.QueryResult;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <p>
 * 访问控制_黑白名单 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-22
 */
@Service
public class ApiAccessRestrictServiceImpl extends ServiceImpl<ApiAccessRestrictMapper, ApiAccessRestrict> implements ApiAccessRestrictService {

    @Autowired
    private ApiAccessRestrictMapper apiAccessRestrictMapper;

    /**
     * 分页查询封堵策略
     *
     * @param apiAccessRestrictQueryDto 查询条件
     * @return 分页结果
     */
    @Override
    public QueryResult<ApiAccessRestrict> getPage(ApiAccessRestrictQueryDto apiAccessRestrictQueryDto) {
        // 设置分页信息 传递参数 页数  每页条数
        Page<ApiAccessRestrict> page = new Page<>(apiAccessRestrictQueryDto.getPageNum(), apiAccessRestrictQueryDto.getPageSize());
        // 查询条件
        Wrapper<ApiAccessRestrict> queryWrapper = getApiAccessRestrictQueryWrapper(apiAccessRestrictQueryDto);
        IPage<ApiAccessRestrict> restrictPageList = apiAccessRestrictMapper.selectPage(page, queryWrapper);
        // 获取查询数据
        List<ApiAccessRestrict> apiAccessRestrictList = restrictPageList.getRecords();
        QueryResult<ApiAccessRestrict> result = new QueryResult<>();
        result.setResultData(apiAccessRestrictList);
        result.setTotalRecord(restrictPageList.getTotal());
        return result;
    }

    /**
     * 构建查询条件
     *
     * @param condition 查询条件
     * @return 查询包装器
     */
    private Wrapper<ApiAccessRestrict> getApiAccessRestrictQueryWrapper(ApiAccessRestrictQueryDto condition) {
        QueryWrapper<ApiAccessRestrict> queryWrapper = new QueryWrapper<>();

        if (condition.getRestrictionType() != null) {
            queryWrapper.eq("restriction_type", condition.getRestrictionType());
        }

        if (StringUtils.isNotBlank(condition.getAction())) {
            queryWrapper.eq("action", condition.getAction());
        }

        if (StringUtils.isNotBlank(condition.getContent())) {
            queryWrapper.like("content", condition.getContent());
        }

        queryWrapper.orderByDesc("id");
        return queryWrapper;
    }

    /**
     * 新增封堵策略
     *
     * @param apiAccessRestrict 封堵策略信息
     * @return 影响行数
     */
    @Override
    @Transactional(propagation = Propagation.REQUIRED)
    public Integer add(ApiAccessRestrict apiAccessRestrict) {
        return apiAccessRestrictMapper.insert(apiAccessRestrict);
    }
}
