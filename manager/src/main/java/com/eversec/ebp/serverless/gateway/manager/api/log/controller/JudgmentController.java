package com.eversec.ebp.serverless.gateway.manager.api.log.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.eversec.ebp.serverless.gateway.manager.api.log.dto.LogLlmResponseQueryDto;
import com.eversec.ebp.serverless.gateway.manager.api.log.entity.LogLlmResponse;
import com.eversec.ebp.serverless.gateway.manager.api.log.service.LogLlmResponseService;
import com.eversec.framework.core.data.QueryResult;
import com.eversec.framework.core.response2.ResponseHolder;
import com.eversec.framework.core.response2.RestResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.codec.ServerSentEvent;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Flux;

import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-31
 */
@RestController
@Api(tags = "网关日志-研判")
@RequestMapping("/api/log/judgment")
public class JudgmentController {

	private static final Logger log = Logger.getLogger(JudgmentController.class.getName());

	@Autowired
	private LogLlmResponseService service;

	@ApiOperation(value = "AI攻击风险研判")
	@RequestMapping(path = "/{batchNo}", method = RequestMethod.GET, produces = MediaType.TEXT_EVENT_STREAM_VALUE)
	public Flux<ServerSentEvent<String>> llmStream(@PathVariable String batchNo, @RequestParam(required = false) String aiName) {
		if ("MAP-大模型攻击检测智能体".equals(aiName)) {
			try {
				return service.getServerSentEventFluxMY(batchNo);
			} catch (Exception e) {
				log.log(Level.SEVERE, "处理异常", e);
				return Flux.just(ServerSentEvent.<String>builder().data("处理失败: " + e.getMessage()).build());
			}
		} else if ("security_eval".equals(aiName)) {
			try {
				return service.getServerSentEventFluxDefault(batchNo);
			} catch (Exception e) {
				log.log(Level.SEVERE, "处理异常", e);
				return Flux.just(ServerSentEvent.<String>builder().data("处理失败: " + e.getMessage()).build());
			}

		} else {
			return Flux.just(ServerSentEvent.<String>builder().data("错误，模型不存在").build());
		}
	}

	@ApiOperation(value = "研判历史列表，按时间倒叙排列")
	@RequestMapping(path = "/list", method = RequestMethod.POST)
	public RestResponse<QueryResult<LogLlmResponse>> list(@RequestBody LogLlmResponseQueryDto params) {
		return ResponseHolder.build(() -> {
			final LambdaQueryWrapper<LogLlmResponse> queryWrapper = Wrappers.lambdaQuery();
			queryWrapper.eq(LogLlmResponse::getBatchNo, params.getBatchNo());
			queryWrapper.orderByDesc(LogLlmResponse::getCreateTime);
			final Page<LogLlmResponse> page = service.page(new Page<>(params.getPageNum(), params.getPageSize()), queryWrapper);
			final QueryResult<LogLlmResponse> queryResult = new QueryResult<>();
			queryResult.setResultData(page.getRecords());
			queryResult.setTotalRecord(page.getTotal());
			return queryResult;
		});
	}

}

