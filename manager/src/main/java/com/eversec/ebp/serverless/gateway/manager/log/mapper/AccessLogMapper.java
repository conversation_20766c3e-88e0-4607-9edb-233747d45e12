package com.eversec.ebp.serverless.gateway.manager.log.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.eversec.ebp.serverless.gateway.manager.api.log.dto.AccessLogQueryDto;
import com.eversec.ebp.serverless.gateway.manager.log.domain.AccessLog;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

/**
 * <p>
 * Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-01-12
 */
@Mapper
public interface AccessLogMapper extends BaseMapper<AccessLog> {

    // 定义SQL片段
    String WHERE_CONDITIONS =
        "<where>" +
            "<if test='query.reqStartTime != null'> AND request_time &gt;= #{query.reqStartTime}</if> " +
            "<if test='query.reqEndTime != null'> AND request_time &lt;= #{query.reqEndTime}</if> " +
            "<if test='query.resHeaderParam != null and query.resHeaderParam.length() > 0'> AND resHeaderParam LIKE CONCAT('%',#{query.resHeaderParam},'%')</if> " +
            "<if test='query.resHeaderParam != null and query.resHeaderParam.length() > 0'> AND response_header LIKE CONCAT('%',#{query.resHeaderParam},'%')</if> " +
            "<if test='query.groupId != null and query.groupId.length() > 0'> AND group_id = #{query.groupId}</if> " +
            "<if test='query.appId != null and query.appId.length() > 0'> AND app_id = #{query.appId}</if> " +
            "<if test='query.id != null and query.id.length() > 0'> AND id = #{query.id}</if> " +
            "<if test='query.statusCode != null'> AND response_status = #{query.statusCode}</if> " +
            "</where>";

    @Select("<script>" +
        "SELECT * FROM log_access " +
        WHERE_CONDITIONS +
        "<if test='query.ascDesc == 0'>" +
        "   ORDER BY request_time desc, response_time asc" +
        "</if>" +
        "<if test='query.ascDesc == 1'>" +
        "   ORDER BY request_time asc, response_time asc" +
        "</if>" +
        "LIMIT #{startPosition}, #{pageSize}" +
        "</script>")
    List<AccessLog> queryAccessLogs(
        @Param("query") AccessLogQueryDto query,
        @Param(value = "startPosition") int startPosition,
        @Param(value = "pageSize") int pageSize);

    @Select("<script>" +
        "SELECT COUNT(id) FROM log_access " +
        WHERE_CONDITIONS +
        "</script>")
    Long countAccessLogs(@Param("query") AccessLogQueryDto query);

    /**
     * 根据批次号查询访问日志
     *
     * @param batchNo 批次号
     * @return 访问日志列表
     */
    @Select("SELECT * FROM log_access WHERE batch_no = #{batchNo} ORDER BY response_time asc ")
    List<AccessLog> queryByBatchNo(@Param("batchNo") String batchNo);


    @Select("select batch_no from log_access where judgment_status=0 order by request_time desc limit 1 ")
    String getOneToJudge();

    @Update("UPDATE log_access SET judgment_status = 1 WHERE batch_no = #{batchNo} ")
    void updateJudgementStatus(String batchNo);


    @Select(" select id from log_access WHERE batch_no = #{batchNo} ")
    List<String> getLogIdList(String batchNo);

    @Select(" select * from log_access WHERE id = #{id} ")
    AccessLog getOneByAccessLogId(String id);
}
