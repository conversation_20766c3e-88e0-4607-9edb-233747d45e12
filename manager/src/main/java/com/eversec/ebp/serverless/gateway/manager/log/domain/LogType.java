package com.eversec.ebp.serverless.gateway.manager.log.domain;

public enum LogType {
	
	/**正常访问日志*/
	ACCESS_LOG,

	/**
     * 阻断日志
     *  访问控制拒绝日志
     *     非规定时间访问
     *     黑名单IP访问
     *     未在白名单IP访问
     *     非规定UA访问
     *     黑名单账号访问
     *     未在白名单的账号访问
     *
     * 流控拒绝日志
     *     API总控QPS超限
     *     基于用户的QPS超限
     *     基于IP的QPS超限
     *     基于应用的QPS超限
     *
     * 认证拒绝日志
     *     账号口令错误
     *     密钥签名错误
     *     证书签名错误
     */
    INTERCEPT_LOG,
    
    /**
     *  监测日志
     *      敏感数据监测日志
     *      特权账号监测日志
     */
    MONITOR_LOG
    

}
