package com.eversec.serverless.gateway.collector.router;

import java.util.Collections;
import java.util.LinkedHashMap;
import java.util.Map;
import org.springframework.cloud.gateway.route.RouteDefinition;
import org.springframework.cloud.gateway.route.RouteDefinitionRepository;

/**
 * 类名称: CustomRouteDefinitionRepository<br>
 * 类描述: 自定义的路由初始化<br>
 * 修改时间: 2021年10月16日<br>
 * <AUTHOR>
 */
public interface CustomRouteDefinitionRepository extends RouteDefinitionRepository {
	
	Map<String, RouteDefinition> ROUTES = Collections.synchronizedMap(new LinkedHashMap<>());

	/**
	 * 从持久化的设备中加载路由到内存<br>
	 * - 从redis中加载<br>
	 * - 从文件中加载<br>
	 */
	void reload();

}
