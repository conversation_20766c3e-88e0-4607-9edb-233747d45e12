package com.eversec.ebp.serverless.gateway.dao.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.eversec.ebp.serverless.gateway.dao.dto.ApiPublishStatus;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * API资产表
 * </p>
 *
 * <AUTHOR> name
 * @since 2025-05-21
 */
@Getter
@Setter
@TableName("api_info")
public class ApiInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id",type = IdType.AUTO)
    private Long id;

    @ApiModelProperty("名称")
    @TableField("name")
    private String name;

    @ApiModelProperty("接口地址")
    @TableField("interface_address")
    private String interfaceAddress;

    @ApiModelProperty("代理路径")
    @TableField("proxy_path")
    private String proxyPath;

    @ApiModelProperty("访问策略id")
    @TableField("access_policy_id")
    private Long accessPolicyId;

    @ApiModelProperty("代理地址")
    @TableField("proxy_address")
    private String proxyAddress;

    @ApiModelProperty("发布状态1:on 0:off")
    @TableField("publish_status")
    private ApiPublishStatus publishStatus;

    @TableLogic
    private Boolean deleted;

    @ApiModelProperty("创建时间")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    @ApiModelProperty("创建人id")
    @TableField("create_user")
    private String createUser;

    @ApiModelProperty("修改时间")
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    @ApiModelProperty("修改人id")
    @TableField("update_user")
    private String updateUser;
}
